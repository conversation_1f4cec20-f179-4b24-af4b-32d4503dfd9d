/**
 * TextToSpeechModel - Abstract Base Class
 *
 * Abstract base class for text-to-speech models.
 * Now extends TextToAudioModel for consistency.
 * Maintains backward compatibility with asset-role system.
 */

import { TextToAudioModel, TextToAudioInput, TextToAudioOutput } from './TextToAudioModel';
import { ModelMetadata, TransformationResult } from './Model';
import { Text, Speech, Audio as RoleAudio } from '../assets/roles';
import { Audio as ModelAudio } from './Audio';
import { TextInput, castToText } from '../assets/casting';

export interface TextToSpeechOptions {
  language?: string;
  speed?: number;
  forceUpload?: boolean;  // Force upload even if file already exists on server
  format?: 'mp3' | 'wav'; // Output audio format
  voice?: string;
  pitch?: number;
  volume?: number;
  quality?: 'low' | 'medium' | 'high';
}

/**
 * Abstract base class for text-to-speech models
 * Now extends TextToAudioModel for unified interface
 */
export abstract class TextToSpeechModel extends TextToAudioModel {
  constructor(metadata: ModelMetadata) {
    // Ensure TTS-specific capabilities are included
    const enhancedMetadata: ModelMetadata = {
      ...metadata,
      capabilities: [...new Set([...metadata.capabilities, 'text-to-speech', 'text-to-audio'])]
    };
    super(enhancedMetadata);
  }

  /**
   * Transform text to audio - implements TextToAudioModel interface
   */
  abstract transform(input: TextToAudioInput): Promise<TransformationResult<TextToAudioOutput>>;

  /**
   * Transform text to speech - backward compatibility method
   * Converts asset-role inputs to TextToAudioModel format
   */
  async transformToSpeech(input: TextInput, options?: TextToSpeechOptions): Promise<Speech> {
    // Cast input to Text
    const text = await castToText(input);

    // Convert to TextToAudioInput format
    const audioInput: TextToAudioInput = {
      text: text.content,
      options: {
        voice: options?.voice,
        speed: options?.speed,
        pitch: options?.pitch,
        volume: options?.volume,
        format: options?.format,
        quality: options?.quality,
        language: options?.language,
        forceUpload: options?.forceUpload
      }
    };

    // Call the main transform method
    const result = await this.transform(audioInput);

    if (!result.success) {
      throw new Error(`TTS transformation failed: ${result.error}`);
    }

    // Convert ModelAudio to RoleAudio (Speech)
    const modelAudio = result.data.audio;
    const roleAudio = new RoleAudio(modelAudio.data, {
      metadata: {
        ...modelAudio.metadata,
        format: modelAudio.format,
        ...result.data.metadata
      }
    });

    return roleAudio.asSpeech();
  }

  /**
   * Transform text to speech with voice cloning - dual-signature pattern
   * Backward compatibility method
   */
  async transformWithVoice(text: TextInput, voiceAudio: Speech, options?: TextToSpeechOptions): Promise<Speech> {
    // This is a specialized method that concrete implementations can override
    // For now, delegate to the basic transform method
    return this.transformToSpeech(text, options);
  }

  /**
   * Check if the model is available
   */
  abstract isAvailable(): Promise<boolean>;

}

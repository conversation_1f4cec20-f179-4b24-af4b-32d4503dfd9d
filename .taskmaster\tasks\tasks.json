{"master": {"tasks": [{"id": 43, "title": "Create ModelRegistry and Provider Base Classes", "description": "Establish the foundational classes for the ModelRegistry and Provider pattern to enable model and provider management.", "details": "Implement abstract base classes `ModelRegistry` and `Provider` with core methods for model registration and retrieval. Use TypeScript interfaces to define the contract for models and providers. Ensure the base classes support role-based model creation and provider delegation.", "testStrategy": "Unit tests for base class methods, including model registration, provider retrieval, and role-based model creation. Verify interface contracts are enforced.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 44, "title": "Extract API Clients from Monolithic Services", "description": "Separate API client logic from existing monolithic services to create pure HTTP API clients.", "details": "Extract API client logic from `ChatterboxTTSDockerService` and `WhisperSTTService` into standalone classes `ChatterboxAPIClient` and `WhisperAPIClient`. Ensure these clients handle only HTTP communication and are free from Docker or business logic.", "testStrategy": "Unit tests for API client methods, ensuring they correctly handle HTTP requests and responses. Mock HTTP calls to verify behavior.", "priority": "high", "dependencies": [43], "status": "done", "subtasks": []}, {"id": 45, "title": "Extract Docker Services from Monolithic Services", "description": "Separate Docker management logic from existing monolithic services to create standalone Docker services.", "details": "Extract Docker management logic from `ChatterboxTTSDockerService` and `WhisperSTTService` into standalone classes `ChatterboxDockerService` and `WhisperDockerService`. These services should handle only Docker operations (start, stop, health checks).", "testStrategy": "Unit tests for Docker service methods, verifying Docker operations (start, stop, health checks) work as expected. Mock Docker interactions for testing.", "priority": "high", "dependencies": [43], "status": "done", "subtasks": []}, {"id": 46, "title": "Create Model Implementations", "description": "Implement concrete model classes that coordinate extracted API clients and Docker services.", "details": "Create concrete model classes (e.g., `WhisperSTTModel`, `ChatterboxTTSModel`) that use the extracted API clients and Docker services for transformation logic. Ensure models adhere to the `Model` interface and delegate infrastructure concerns to providers.", "testStrategy": "Unit tests for model transformation methods, ensuring they correctly use API clients and Docker services. Mock dependencies to isolate model logic.", "priority": "high", "dependencies": [44, 45], "status": "done", "subtasks": []}, {"id": 47, "title": "Create Provider Classes", "description": "Implement provider classes that manage model creation and infrastructure.", "details": "Create provider classes (e.g., `WhisperDockerProvider`, `ChatterboxDockerProvider`) that implement the `Provider` interface. These classes should manage Docker services and create model instances with pre-configured dependencies.", "testStrategy": "Unit tests for provider methods, verifying model creation and infrastructure management. Mock dependencies to isolate provider logic.", "priority": "high", "dependencies": [43, 46], "status": "done", "subtasks": []}, {"id": 48, "title": "Register Models with Providers", "description": "Implement model registration logic in the ModelRegistry to associate models with their providers.", "details": "Extend the `ModelRegistry` to support model registration with providers. Ensure providers can register models they support, and the registry can retrieve models by provider and model ID.", "testStrategy": "Unit tests for model registration and retrieval in the `ModelRegistry`. Verify models are correctly associated with providers.", "priority": "medium", "dependencies": [43, 47], "status": "pending", "subtasks": []}, {"id": 49, "title": "Update Existing Code to Use New Pattern", "description": "Refactor existing code to use the new ModelRegistry and Provider pattern.", "details": "Update existing code (e.g., `ChatterboxTTSDockerService`, `WhisperSTTService`) to use the new `ModelRegistry` and `Provider` pattern. Ensure backward compatibility with existing API interfaces.", "testStrategy": "Integration tests to verify existing functionality works with the new pattern. Ensure all unit tests pass.", "priority": "medium", "dependencies": [47, 48], "status": "pending", "subtasks": []}, {"id": 50, "title": "Implement Asset and Role Mixins", "description": "Create the base Asset class and role mixins for strong typing and role casting.", "details": "Implement the base `Asset` class and role mixins (`withSpeechRole`, `withAudioRole`, etc.) to enable strong typing and automatic role casting. Ensure role mixins preserve source asset references.", "testStrategy": "Unit tests for asset creation, role casting, and source asset preservation. Verify role interfaces are correctly applied.", "priority": "medium", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 51, "title": "Implement Core Media Types", "description": "Create core media types (Speech, Audio, Video, Text) with source asset preservation.", "details": "Implement the core media types (`Speech`, `Audio`, `Video`, `Text`) with constructors that preserve source asset references. Ensure these types can be created from assets and cast to other roles.", "testStrategy": "Unit tests for media type creation, role casting, and source asset preservation. Verify media types correctly reference their source assets.", "priority": "medium", "dependencies": [50], "status": "pending", "subtasks": []}, {"id": 52, "title": "Implement Model Interfaces with Role Acceptance", "description": "Define model interfaces that accept role-based inputs and outputs.", "details": "Define abstract model interfaces (e.g., `SpeechToTextModel`, `TextToSpeechModel`) that accept role-based inputs (e.g., `Speech | (Asset & SpeechRole)`). Ensure these interfaces enforce type-safe role casting.", "testStrategy": "Unit tests for model interfaces, verifying they correctly accept role-based inputs and outputs. Mock role casting for testing.", "priority": "medium", "dependencies": [50, 51], "status": "pending", "subtasks": []}, {"id": 53, "title": "Implement Provider Role Mixins", "description": "Create role mixins for providers to enable multi-role capabilities.", "details": "Implement role mixins (e.g., `withSpeechToTextProvider`, `withTextToSpeechProvider`) to enable providers to support multiple roles (e.g., STT and TTS). Ensure mixins correctly extend provider capabilities.", "testStrategy": "Unit tests for provider role mixins, verifying they correctly extend provider capabilities. Mock role implementations for testing.", "priority": "medium", "dependencies": [47, 52], "status": "pending", "subtasks": []}, {"id": 54, "title": "Implement Factory Pattern for Provider Selection", "description": "Create a factory class to retrieve providers by role.", "details": "Implement a `ProviderFactory` class with methods to retrieve providers by role (e.g., `getSpeechToTextProvider`). Ensure the factory supports manual provider selection and role-based retrieval.", "testStrategy": "Unit tests for the `ProviderFactory`, verifying it correctly retrieves providers by role. Mock provider registration for testing.", "priority": "medium", "dependencies": [47, 53], "status": "pending", "subtasks": []}, {"id": 55, "title": "Verify All Tests Pass", "description": "Run all unit and integration tests to ensure the refactored code meets requirements.", "details": "Execute all existing unit and integration tests to verify the refactored code maintains existing functionality. Address any test failures and ensure backward compatibility.", "testStrategy": "Run all unit and integration tests. Verify no regressions and all tests pass.", "priority": "high", "dependencies": [49, 54], "status": "pending", "subtasks": []}, {"id": 56, "title": "Document Architecture and Usage", "description": "Create documentation for the new architecture and usage patterns.", "details": "Document the new ModelRegistry and Provider architecture, including examples of model and provider usage. Provide guidelines for extending the system with new models and providers.", "testStrategy": "Review documentation for accuracy and completeness. Verify examples work as described.", "priority": "low", "dependencies": [55], "status": "pending", "subtasks": []}, {"id": 57, "title": "Demo Core Architecture Principle", "description": "Demonstrate the core architecture principle of one model, multiple providers.", "details": "Create a demo showcasing the core architecture principle: the same model interface (`whisper`) working with different providers (Docker, OpenAI, Replicate). Highlight provider abstraction and implementation flexibility.", "testStrategy": "Verify the demo correctly demonstrates the architecture principle. Ensure all provider implementations work as expected.", "priority": "low", "dependencies": [55], "status": "pending", "subtasks": []}, {"id": 58, "title": "Update TextToSpeechModel Interface for Dual-Signature Transform Method", "description": "Modify the TextToSpeechModel interface to support a dual-signature transform method for voice cloning, changing from voiceFile options to a clean interface pattern.", "details": "1. Update the TextToSpeechModel interface:\n   - Modify the existing transform method signature:\n     From: transform(text: string, options?: { voiceFile: string }): Promise<Speech>\n     To: transform(text: Text): Promise<Speech>\n   - Add a new overload for voice cloning:\n     transform(text: Text, voiceAudio: Audio): Promise<Speech>\n\n2. Update the implementation in relevant classes (e.g., ChatterboxTTSModel):\n   - Implement both transform method signatures\n   - For the voice cloning method, extract the audio data from the Audio object\n\n3. Modify the Audio class to include a static method:\n   Add: static fromFile(filePath: string): Audio\n\n4. Update PRD documentation:\n   - Explain the new interface design and its benefits\n   - Provide usage examples for both basic TTS and voice cloning\n\n5. Update interface definitions in TypeScript:\n   ```typescript\n   interface TextToSpeechModel {\n     transform(text: Text): Promise<Speech>;\n     transform(text: Text, voiceAudio: Audio): Promise<Speech>;\n   }\n   ```\n\n6. Refactor existing code that uses the old interface:\n   - Search for all occurrences of `transform` method calls with `voiceFile` option\n   - Replace with new `Audio.fromFile()` method and updated transform call\n\n7. Ensure backward compatibility:\n   - If necessary, create a wrapper method that accepts the old format and calls the new method internally\n\n8. Update any relevant factory methods or dependency injection code to accommodate the new interface", "testStrategy": "1. Unit Tests:\n   - Test basic TTS transform method with various text inputs\n   - Test voice cloning transform method with different text and audio inputs\n   - Verify Audio.fromFile() method correctly loads audio files\n   - Test error handling for invalid inputs (e.g., null or undefined text/audio)\n\n2. Integration Tests:\n   - Test the entire TTS pipeline using both transform methods\n   - Verify output Speech object contains correct data\n   - Test with real audio files for voice cloning\n\n3. Backward Compatibility Tests:\n   - If a compatibility wrapper is implemented, test it thoroughly\n   - Ensure existing code using the old interface still works correctly\n\n4. Performance Tests:\n   - Compare performance of new interface vs. old interface\n   - Ensure voice cloning doesn't introduce significant overhead\n\n5. Documentation Tests:\n   - Verify all code examples in updated PRD documentation are correct and functional\n\n6. Edge Case Tests:\n   - Test with very long text inputs\n   - Test with various audio file formats and qualities for voice cloning\n\n7. Mock Provider Tests:\n   - Create mock TextToSpeechModel implementations to test interface compliance\n\n8. Regression Tests:\n   - Run full test suite to ensure no unintended side effects in other parts of the system", "status": "done", "dependencies": [52, 51], "priority": "high", "subtasks": [{"id": 1, "title": "Update Chatterbox Integration Test", "description": "Update the chatterbox integration test to demonstrate the new dual-signature transform pattern for voice cloning", "details": "Replace the current voice cloning test that uses options with a clean dual-signature interface test that matches the Whisper STT pattern. The test should show:\n- Basic TTS: model.transform(text) \n- Voice cloning: model.transform(text, voiceAudio)\n- Clear comparison between the two approaches", "status": "done", "dependencies": [], "parentTaskId": 58}, {"id": 2, "title": "Update PRD Documentation", "description": "Update PRD documentation to reflect the dual-signature TTS transform pattern", "details": "Updated the PRD to include:\n- New TTS Dual-Signature Pattern section explaining the design\n- Updated TextToSpeechModel interface with overloaded transform methods\n- Updated usage examples showing both basic TTS and voice cloning patterns\n- Clear explanation of consistency with Whisper STT multi-input pattern", "status": "done", "dependencies": [], "parentTaskId": 58}, {"id": 3, "title": "Implement Dual-Signature Transform in TextToSpeechModel", "description": "Implement the dual-signature transform method in TextToSpeechModel abstract class", "details": "Update the abstract TextToSpeechModel class to support method overloading:\n- transform(input: Text | (Asset & TextRole)): Promise<Speech> - basic TTS\n- transform(text: Text | (Asset & TextRole), voiceAudio: Audio | (Asset & SpeechRole)): Promise<Speech> - voice cloning\nEnsure type safety and proper method resolution for both signatures.", "status": "done", "dependencies": [], "parentTaskId": 58}, {"id": 4, "title": "Update Chatterbox Implementation", "description": "Update ChatterboxDockerProvider and ChatterboxTTSModel to implement dual-signature interface", "details": "Modify the concrete implementation to support both transform signatures:\n- Remove voiceFile from options interface\n- Implement basic TTS: transform(text) calling existing TTS API\n- Implement voice cloning: transform(text, voiceAudio) with voice upload and cloning API calls\n- Ensure proper Audio asset handling and file upload for voice cloning", "status": "done", "dependencies": [], "parentTaskId": 58}]}], "metadata": {"created": "2025-06-17T05:23:26.699Z", "updated": "2025-06-17T23:19:33.377Z", "description": "Tasks for master context"}}}
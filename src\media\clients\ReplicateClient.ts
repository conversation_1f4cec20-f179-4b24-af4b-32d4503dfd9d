/**
 * ReplicateClient - Enhanced API Client with Hybrid Model Discovery
 * 
 * Advanced abstraction around the Replicate API that provides:
 * - Unified interface for all Replicate models
 * - Hybrid model discovery (API + caching + AI categorization)
 * - Complete parameter extraction from OpenAPI schemas
 * - Error handling and retry logic
 * - Rate limiting and cost tracking
 * - Type-safe request/response handling
 */

import fetch, { Response } from 'node-fetch';
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface ReplicateConfig {
  apiKey: string;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requestsPerSecond?: number;
    requestsPerMinute?: number;
  };
  discovery?: {
    openRouterApiKey?: string;
    cacheDir?: string;
    maxCacheAge?: number; // in milliseconds
  };
}

export interface ReplicateModelMetadata {
  id: string; // owner/name format
  owner: string;
  name: string;
  description: string;
  category: string; // AI-categorized
  tags: string[];
  visibility: 'public' | 'private';
  run_count: number;
  github_url?: string;
  paper_url?: string;
  license_url?: string;
  cover_image_url?: string;
  parameters: Record<string, {
    type: string;
    description: string;
    required?: boolean;
    default?: any;
    minimum?: number;
    maximum?: number;
    enum?: string[];
    x_order?: number;
  }>;
  output_schema?: any;
  capabilities: string[];
  pricing?: string;
  lastUpdated: number;
}

export interface ReplicateRequestOptions {
  model: string; // owner/name format
  input: Record<string, any>;
  webhook?: string;
  webhook_events_filter?: string[];
}

export interface ReplicateResponse<T = any> {
  id: string;
  model: string;
  version: string;
  input: Record<string, any>;
  output?: T;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  error?: string;
  logs?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  metrics?: {
    predict_time: number;
  };
  urls: {
    get: string;
    cancel: string;
  };
}

export interface ReplicatePaginatedResponse<T> {
  next?: string;
  previous?: string;
  results: T[];
}

export class ReplicateError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public requestId?: string
  ) {
    super(message);
    this.name = 'ReplicateError';
  }
}

/**
 * Enhanced Replicate client with hybrid model discovery
 */
export class ReplicateClient {
  private config: ReplicateConfig;
  private requestCount = 0;
  private lastRequestTime = 0;
  private cacheFile: string;

  constructor(config: ReplicateConfig) {
    this.config = config;
    this.cacheFile = path.join(
      config.discovery?.cacheDir || process.cwd(),
      'replicate_model_data.json'
    );
    this.validateConfig();
  }

  private validateConfig(): void {
    if (!this.config.apiKey) {
      throw new ReplicateError('Replicate API key is required');
    }
    if (!this.config.apiKey.startsWith('r8_')) {
      throw new ReplicateError('Invalid Replicate API key format');
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/account');
      return response.ok;
    } catch (error) {
      console.warn('Replicate connection test failed:', error);
      return false;
    }
  }

  /**
   * Get model metadata with hybrid discovery approach
   */
  async getModelMetadata(modelId: string): Promise<ReplicateModelMetadata> {
    // 1. Try loading from cache first
    const cached = await this.loadFromCache(modelId);
    
    // 2. Check if cache is fresh
    const maxAge = this.config.discovery?.maxCacheAge || (24 * 60 * 60 * 1000); // 24 hours
    if (cached && (Date.now() - cached.lastUpdated) < maxAge) {
      return cached;
    }

    // 3. Cache miss or stale - fetch from API and enhance with AI
    console.log(`🔍 Discovering metadata for ${modelId}...`);
    const metadata = await this.discoverModelMetadata(modelId);
    
    // 4. Store in cache
    await this.saveToCache(modelId, metadata);
    
    return metadata;
  }

  /**
   * Discover model metadata using Replicate API + AI categorization
   */
  private async discoverModelMetadata(modelId: string): Promise<ReplicateModelMetadata> {
    try {
      // 1. Fetch model details from Replicate API
      const modelResponse = await this.makeRequest(`/models/${modelId}`);
      if (!modelResponse.ok) {
        throw new Error(`Failed to fetch model: ${modelResponse.status}`);
      }

      const modelData = await modelResponse.json() as any;

      // 2. Extract parameters from OpenAPI schema
      const parameters = this.extractParametersFromSchema(
        modelData.latest_version?.openapi_schema
      );

      // 3. Use AI to categorize and enhance metadata
      let category = 'unknown';
      let capabilities: string[] = [];
      
      if (this.config.discovery?.openRouterApiKey) {
        const aiEnhancement = await this.enhanceWithAI(modelData);
        category = aiEnhancement.category;
        capabilities = aiEnhancement.capabilities;
      } else {
        // Fallback: simple categorization based on description
        category = this.basicCategorization(modelData.description || '');
      }

      // 4. Build complete metadata
      return {
        id: modelId,
        owner: modelData.owner,
        name: modelData.name,
        description: modelData.description || 'No description available',
        category,
        tags: this.extractTags(modelData.description || ''),
        visibility: modelData.visibility,
        run_count: modelData.run_count || 0,
        github_url: modelData.github_url,
        paper_url: modelData.paper_url,
        license_url: modelData.license_url,
        cover_image_url: modelData.cover_image_url,
        parameters,
        output_schema: modelData.latest_version?.openapi_schema?.components?.schemas?.Output,
        capabilities,
        pricing: 'usage-based', // Replicate uses usage-based pricing
        lastUpdated: Date.now()
      };

    } catch (error) {
      throw new ReplicateError(
        `Failed to discover model metadata: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Extract parameters from OpenAPI schema
   */
  private extractParametersFromSchema(schema: any): ReplicateModelMetadata['parameters'] {
    const parameters: ReplicateModelMetadata['parameters'] = {};
    
    if (!schema?.components?.schemas?.Input?.properties) {
      return parameters;
    }

    const props = schema.components.schemas.Input.properties;
    const required = schema.components.schemas.Input.required || [];

    for (const [name, prop] of Object.entries(props)) {
      const paramDef = prop as any;
      
      parameters[name] = {
        type: paramDef.type || 'string',
        description: paramDef.description || paramDef.title || '',
        required: required.includes(name),
        default: paramDef.default,
        minimum: paramDef.minimum,
        maximum: paramDef.maximum,
        enum: paramDef.enum || (paramDef.allOf?.[0]?.enum),
        x_order: paramDef['x-order'] || 0
      };
    }

    return parameters;
  }

  /**
   * Enhance metadata with AI categorization
   */
  private async enhanceWithAI(modelData: any): Promise<{category: string, capabilities: string[]}> {
    try {
      const aiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.discovery!.openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://automarket.ai',
          'X-Title': 'AutoMarket Model Discovery'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat:free',
          messages: [
            {
              role: 'system',
              content: `Categorize this Replicate model and extract capabilities. Return valid JSON:
{
  "category": "text-to-image|image-to-video|text-to-video|video-to-video|text-to-speech|speech-to-text|image-to-image|text-to-text|other",
  "capabilities": ["capability1", "capability2", "capability3"]
}

Base the category on the model's purpose and capabilities.`
            },
            {
              role: 'user',
              content: `Model: ${modelData.owner}/${modelData.name}
Description: ${modelData.description || 'No description'}
Owner: ${modelData.owner}
Run Count: ${modelData.run_count || 0}
GitHub: ${modelData.github_url || 'N/A'}`
            }
          ],
          response_format: { type: 'json_object' },
          max_tokens: 200,
          temperature: 0.1
        })
      });

      if (aiResponse.ok) {
        const aiResult = await aiResponse.json() as any;
        const parsed = JSON.parse(aiResult.choices[0].message.content);
        return {
          category: parsed.category || 'unknown',
          capabilities: parsed.capabilities || []
        };
      }
    } catch (error) {
      console.warn('AI enhancement failed:', error);
    }

    return { category: 'unknown', capabilities: [] };
  }

  /**
   * Basic categorization without AI
   */
  private basicCategorization(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('image') && desc.includes('video')) return 'image-to-video';
    if (desc.includes('text') && desc.includes('image')) return 'text-to-image';
    if (desc.includes('text') && desc.includes('video')) return 'text-to-video';
    if (desc.includes('video') && desc.includes('video')) return 'video-to-video';
    if (desc.includes('speech') || desc.includes('audio')) return 'text-to-speech';
    if (desc.includes('transcrib')) return 'speech-to-text';
    if (desc.includes('image')) return 'image-to-image';
    if (desc.includes('text') || desc.includes('language')) return 'text-to-text';
    
    return 'other';
  }

  /**
   * Extract tags from description
   */
  private extractTags(description: string): string[] {
    const commonTags = [
      'diffusion', 'stable diffusion', 'flux', 'sdxl', 'dalle',
      'video generation', 'image generation', 'upscaling', 'super resolution',
      'face swap', 'voice cloning', 'music generation', 'code generation'
    ];
    
    const desc = description.toLowerCase();
    return commonTags.filter(tag => desc.includes(tag));
  }
  /**
   * List all public models with pagination
   */
  async listModels(cursor?: string): Promise<ReplicatePaginatedResponse<any>> {
    const url = cursor || '/models';
    const response = await this.makeRequest(url);
    return await response.json() as ReplicatePaginatedResponse<any>;
  }

  /**
   * Discover models by collection
   */
  async getModelsByCollection(collectionSlug: string): Promise<any[]> {
    const response = await this.makeRequest(`/collections/${collectionSlug}`);
    const collection = await response.json() as any;
    return collection.models || [];
  }
  /**
   * Search models (using query parameter instead of body)
   */
  async searchModels(query: string): Promise<ReplicatePaginatedResponse<any>> {
    const response = await this.makeRequest(`/models?search=${encodeURIComponent(query)}`);
    return await response.json() as ReplicatePaginatedResponse<any>;
  }

  /**
   * Run a prediction
   */
  async predict<T = any>(options: ReplicateRequestOptions): Promise<ReplicateResponse<T>> {
    await this.enforceRateLimit();
    
    try {
      const response = await this.makeRequest(`/models/${options.model}/predictions`, 'POST', {
        input: options.input,
        webhook: options.webhook,
        webhook_events_filter: options.webhook_events_filter
      });

      if (!response.ok) {
        throw new ReplicateError(`Prediction failed: ${response.status}`);
      }      return await response.json() as ReplicateResponse<T>;
    } catch (error) {
      throw new ReplicateError(
        `Prediction failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Get prediction status
   */
  async getPrediction(predictionId: string): Promise<ReplicateResponse> {
    const response = await this.makeRequest(`/predictions/${predictionId}`);
    return await response.json() as ReplicateResponse;
  }

  /**
   * Cancel prediction
   */
  async cancelPrediction(predictionId: string): Promise<void> {
    await this.makeRequest(`/predictions/${predictionId}/cancel`, 'POST');
  }

  /**
   * Load model metadata from cache
   */
  private async loadFromCache(modelId: string): Promise<ReplicateModelMetadata | null> {
    try {
      const cacheData = await fs.readFile(this.cacheFile, 'utf-8');
      const cache = JSON.parse(cacheData) as Record<string, ReplicateModelMetadata>;
      return cache[modelId] || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Save model metadata to cache
   */
  private async saveToCache(modelId: string, metadata: ReplicateModelMetadata): Promise<void> {
    try {
      let cache: Record<string, ReplicateModelMetadata> = {};
      
      // Load existing cache
      try {
        const cacheData = await fs.readFile(this.cacheFile, 'utf-8');
        cache = JSON.parse(cacheData);
      } catch (error) {
        // Cache file doesn't exist, will create new one
      }

      // Update cache
      cache[modelId] = metadata;

      // Ensure cache directory exists
      const dir = path.dirname(this.cacheFile);
      await fs.mkdir(dir, { recursive: true });

      // Save cache
      await fs.writeFile(this.cacheFile, JSON.stringify(cache, null, 2));
      
      console.log(`💾 Cached metadata for ${modelId}`);
    } catch (error) {
      console.warn('Failed to save model cache:', error);
    }
  }

  /**
   * Get all cached models
   */
  async getCachedModels(): Promise<ReplicateModelMetadata[]> {
    try {
      const cacheData = await fs.readFile(this.cacheFile, 'utf-8');
      const cache = JSON.parse(cacheData) as Record<string, ReplicateModelMetadata>;
      return Object.values(cache);
    } catch (error) {
      return [];
    }
  }

  /**
   * Clear model cache
   */
  async clearCache(): Promise<void> {
    try {
      await fs.unlink(this.cacheFile);
      console.log('🗑️  Model cache cleared');
    } catch (error) {
      // Cache file doesn't exist
    }
  }
  /**
   * Make HTTP request to Replicate API
   */
  private async makeRequest(
    endpoint: string, 
    method: 'GET' | 'POST' | 'DELETE' | 'PATCH' = 'GET',
    body?: any
  ): Promise<Response> {
    const url = endpoint.startsWith('http') 
      ? endpoint 
      : `https://api.replicate.com/v1${endpoint}`;

    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'User-Agent': 'AutoMarket-ReplicateClient/1.0'
    };

    if (body) {
      headers['Content-Type'] = 'application/json';
    }

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
      timeout: this.config.timeout || 30000
    } as any);

    if (!response.ok && response.status >= 400) {
      const errorText = await response.text();
      throw new ReplicateError(
        `API request failed: ${response.status} ${errorText}`,
        response.status
      );
    }

    return response;
  }

  /**
   * Enforce rate limiting
   */
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    const rateLimit = this.config.rateLimit;
    if (rateLimit?.requestsPerSecond) {
      const minInterval = 1000 / rateLimit.requestsPerSecond;
      if (timeSinceLastRequest < minInterval) {
        await new Promise(resolve => setTimeout(resolve, minInterval - timeSinceLastRequest));
      }
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Get client statistics
   */
  getStats(): {
    requestCount: number;
    lastRequestTime: number;
    isConfigured: boolean;
  } {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      isConfigured: !!this.config.apiKey
    };
  }
}

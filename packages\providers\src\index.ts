/**
 * AutoMarket Providers Package
 * 
 * Exports all provider implementations and utilities
 */

// Core provider implementations
export { ReplicateProvider } from './remote/ReplicateProvider';
export { CreatifyAdapter } from './remote/CreatifyProvider';
export { ChatterboxProvider } from './local/ChatterboxProvider';

// Legacy adapters (to be migrated to new provider system)
// export { ReplicateAdapter } from './adapters/ReplicateAdapter';
// export { FalAiAdapter } from './adapters/FalAiAdapter';

// Re-export core types for convenience
export type {
  MediaProvider,
  MediaCapability,
  ProviderType,
  JobStatus,
  ProviderModel,
  ProviderConfig,
  GenerationRequest,
  GenerationResult
} from '../../../packages/core/src';

/**
 * TextToAudioProvider Interface
 * 
 * Provider role for text-to-audio transformation capabilities.
 * Includes text-to-speech as the primary use case.
 */

import { TextToAudioModel } from '../../../models/TextToAudioModel';
import { ServiceManagement } from '../ServiceManagement';

/**
 * Text-to-Audio Provider Role (includes text-to-speech)
 */
export interface TextToAudioProvider extends ServiceManagement {
  createTextToAudioModel(modelId: string): Promise<TextToAudioModel>;
  getSupportedTextToAudioModels(): string[];
  supportsTextToAudioModel(modelId: string): boolean;
}

/**
 * Text-to-Speech Provider Role (alias for TextToAudioProvider)
 */
export interface TextToSpeechProvider extends TextToAudioProvider {
  // Alias methods for backward compatibility
  createTextToSpeechModel(modelId: string): Promise<TextToAudioModel>;
  getSupportedTextToSpeechModels(): string[];
  supportsTextToSpeechModel(modelId: string): boolean;
}

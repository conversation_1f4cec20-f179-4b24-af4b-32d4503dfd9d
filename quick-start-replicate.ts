/**
 * Quick Start: ReplicateProvider Usage
 * 
 * Simple example showing the basic usage of ReplicateProvider
 */

import { ReplicateProvider } from './packages/providers/src/remote/ReplicateProvider';
import { Text } from './src/media/assets/roles';

async function quickStart() {
  // 1. Create and configure provider
  const provider = new ReplicateProvider();  await provider.configure({
    apiKey: process.env.REPLICATE_API_TOKEN || process.env.REPLICATE_API_KEY || 'r8_your_api_key_here'
  });  // 2. Generate a video from text
  console.log('🎬 Generating video...');
  const videoModel = await provider.createTextToVideoModel('minimax/video-01');
  console.log('📋 Model created:', typeof videoModel, Object.keys(videoModel || {}));
  
  const text = Text.fromString('A cat playing piano');
  const video = await videoModel.transform(text, {
    duration: 5,
    aspectRatio: '16:9'
  });
  console.log('✅ Video generated and downloaded!');
  console.log('📹 Video URL:', (video as any).metadata?.url);
  console.log('💾 Local file:', (video as any).metadata?.localPath);
  console.log('📊 File size:', ((video as any).metadata?.fileSize / 1024 / 1024).toFixed(2), 'MB');
  console.log('⏱️  Duration:', video.getDuration());
  console.log('📐 Size:', video.getDimensions());

  // Optional: Copy to a custom location
  if ((video as any).metadata?.localPath) {
    const fs = require('fs');
    const customPath = './my-video.mp4';
    fs.copyFileSync((video as any).metadata.localPath, customPath);
    console.log('📋 Copied to:', customPath);
  }
}

// Run it
quickStart().catch(console.error);

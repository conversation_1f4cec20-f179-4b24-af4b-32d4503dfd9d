/**
 * Enhanced ReplicateTextToVideoModel with File Saving
 * 
 * Extends the basic model to include content downloading capabilities
 */

import { ReplicateTextToVideoModel } from './src/media/models/ReplicateTextToVideoModel';
import { TextToVideoOptions } from './src/media/models/TextToVideoModel';
import { Video } from './src/media/assets/roles';
import { TextInput } from './src/media/assets/casting';
import * as fs from 'fs';
import * as path from 'path';
import fetch from 'node-fetch';

export class ReplicateTextToVideoModelWithSaving extends ReplicateTextToVideoModel {
  
  /**
   * Transform text to video and save to disk
   */
  async transformAndSave(
    input: TextInput, 
    options?: TextToVideoOptions & { 
      outputPath?: string;
      downloadTimeout?: number;
    }
  ): Promise<{ video: Video; savedPath?: string }> {
    
    // Generate video normally
    const video = await this.transform(input, options);
    
    // Download and save if outputPath is provided
    let savedPath: string | undefined;
    
    if (options?.outputPath) {
      const videoUrl = (video as any).metadata?.url;
      
      if (videoUrl) {
        savedPath = await this.downloadVideo(videoUrl, options.outputPath, options.downloadTimeout);
        
        // Update video metadata with local path
        if ((video as any).metadata) {
          (video as any).metadata.localPath = savedPath;
        }
      } else {
        console.warn('No video URL found in result, cannot download');
      }
    }
    
    return { video, savedPath };
  }

  /**
   * Download video from URL to local path
   */
  private async downloadVideo(url: string, outputPath: string, timeout: number = 60000): Promise<string> {
    console.log(`📥 Downloading video: ${url}`);
    
    // Ensure output directory exists
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Download timeout')), timeout);
    });

    // Download with timeout
    const downloadPromise = (async () => {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`);
      }

      const buffer = await response.buffer();
      fs.writeFileSync(outputPath, buffer);
      
      console.log(`💾 Video saved: ${outputPath} (${(buffer.length / 1024 / 1024).toFixed(2)} MB)`);
      return outputPath;
    })();

    return Promise.race([downloadPromise, timeoutPromise]) as Promise<string>;
  }

  /**
   * Generate multiple videos and save them
   */
  async batchGenerateAndSave(
    prompts: string[],
    options: TextToVideoOptions & {
      outputDir: string;
      filenamePrefix?: string;
    }
  ): Promise<Array<{ prompt: string; video?: Video; savedPath?: string; error?: string }>> {
    
    const results: Array<{ prompt: string; video?: Video; savedPath?: string; error?: string }> = [];
    
    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      console.log(`🎬 Generating video ${i + 1}/${prompts.length}: "${prompt}"`);
      
      try {
        const filename = `${options.filenamePrefix || 'video'}_${i + 1}.mp4`;
        const outputPath = path.join(options.outputDir, filename);
        
        const { video, savedPath } = await this.transformAndSave(prompt, {
          ...options,
          outputPath,
          seed: i * 42 // Different seed for each video
        });
        
        results.push({ prompt, video, savedPath });
        console.log(`✅ Video ${i + 1} completed!`);
        
      } catch (error) {
        console.log(`❌ Video ${i + 1} failed:`, error.message);
        results.push({ prompt, error: error.message });
      }
    }
    
    return results;
  }
}

// Usage example
async function exampleUsage() {
  const model = new ReplicateTextToVideoModelWithSaving({
    // ... configuration
  } as any);

  // Single video with saving
  const { video, savedPath } = await model.transformAndSave(
    "A dragon flying over a castle",
    {
      duration: 5,
      aspectRatio: '16:9',
      outputPath: './output/dragon_video.mp4'
    }
  );

  console.log('Video generated and saved to:', savedPath);

  // Batch generation
  const prompts = [
    "A sunset over the ocean",
    "A cat playing with yarn",
    "Northern lights in the sky"
  ];

  const results = await model.batchGenerateAndSave(prompts, {
    duration: 3,
    aspectRatio: '1:1',
    outputDir: './output/batch',
    filenamePrefix: 'nature'
  });

  console.log('Batch generation complete:', results.length, 'videos processed');
}

export default ReplicateTextToVideoModelWithSaving;

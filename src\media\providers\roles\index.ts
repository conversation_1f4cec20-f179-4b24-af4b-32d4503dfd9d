/**
 * Provider Roles - Main Export
 *
 * Centralized export for all provider role interfaces, mixins, and guards.
 * This provides a clean API for importing provider role functionality.
 */

// Base interfaces
export { ServiceManagement } from './ServiceManagement';

// Role interfaces
export {
  AudioToTextProvider,
  SpeechToTextProvider
} from './interfaces/AudioToTextProvider';

export {
  TextToAudioProvider,
  TextToSpeechProvider
} from './interfaces/TextToAudioProvider';

export { VideoToAudioProvider } from './interfaces/VideoToAudioProvider';
export { TextToVideoProvider } from './interfaces/TextToVideoProvider';
export { VideoToVideoProvider } from './interfaces/VideoToVideoProvider';
export { TextToImageProvider } from './interfaces/TextToImageProvider';
export { TextGenerationProvider } from './interfaces/TextGenerationProvider';

// Mixins
export {
  withAudioToTextProvider,
  withSpeechToTextProvider
} from './mixins/AudioToTextMixin';
export type { Constructor as AudioToTextConstructor } from './mixins/AudioToTextMixin';

export {
  withTextToAudioProvider,
  withTextToSpeechProvider
} from './mixins/TextToAudioMixin';
export type { Constructor as TextToAudioConstructor } from './mixins/TextToAudioMixin';

export {
  withVideoToAudioProvider
} from './mixins/VideoToAudioMixin';
export type { Constructor as VideoToAudioConstructor } from './mixins/VideoToAudioMixin';

export {
  withTextToImageProvider
} from './mixins/TextToImageMixin';
export type { Constructor as TextToImageConstructor } from './mixins/TextToImageMixin';

export {
  withTextToVideoProvider
} from './mixins/TextToVideoMixin';
export type { Constructor as TextToVideoConstructor } from './mixins/TextToVideoMixin';

export {
  withVideoToVideoProvider
} from './mixins/VideoToVideoMixin';
export type { Constructor as VideoToVideoConstructor } from './mixins/VideoToVideoMixin';

// Guards
export {
  hasAudioToTextRole,
  hasSpeechToTextRole,
  hasTextToAudioRole,
  hasTextToSpeechRole,
  hasVideoToAudioRole,
  hasTextToVideoRole,
  hasVideoToVideoRole,
  hasTextToImageRole,
  hasTextGenerationRole,
  getProviderRoles
} from './guards/ProviderRoleGuards';

// Re-export common types
export type Constructor<T = {}> = new (...args: any[]) => T;
/**
 * AudioToTextProvider Interface
 * 
 * Provider role for audio-to-text transformation capabilities.
 * Includes speech-to-text as a subset of audio-to-text.
 */

import { SpeechToTextModel } from '../../../models/SpeechToTextModel';
import { ServiceManagement } from '../ServiceManagement';

/**
 * Audio-to-Text Provider Role (includes speech-to-text functionality)
 */
export interface AudioToTextProvider extends ServiceManagement {
  createAudioToTextModel(modelId: string): Promise<SpeechToTextModel>;
  getSupportedAudioToTextModels(): string[];
  supportsAudioToTextModel(modelId: string): boolean;
}

/**
 * Speech-to-Text Provider Role (alias for AudioToTextProvider)
 */
export interface SpeechToTextProvider extends AudioToTextProvider {
  // Alias methods for backward compatibility
  createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel>;
  getSupportedSpeechToTextModels(): string[];
  supportsSpeechToTextModel(modelId: string): boolean;
}

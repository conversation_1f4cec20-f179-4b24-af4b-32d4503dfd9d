/**
 * AudioToTextMixin
 * 
 * Mixin to add AudioToTextProvider capabilities to a provider class.
 */

import { SpeechToTextModel } from '../../../models/SpeechToTextModel';
import { AudioToTextProvider, SpeechToTextProvider } from '../interfaces/AudioToTextProvider';

/**
 * Constructor type for mixin functions
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

/**
 * Add AudioToTextProvider capabilities to a provider
 */
export function withAudioToTextProvider<T extends Constructor>(Base: T) {
  return class extends Base implements AudioToTextProvider, SpeechToTextProvider {
    async createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel> {
      // Delegate to base provider's getModel method
      const model = await (this as any).getModel(modelId);

      if (!(model instanceof SpeechToTextModel)) {
        throw new Error(`Model '${modelId}' is not a SpeechToTextModel`);
      }

      return model;
    }

    getSupportedSpeechToTextModels(): string[] {
      // Filter supported models to only STT models
      const allModels = (this as any).getSupportedModels();
      return allModels.filter((modelId: string) => 
        modelId.includes('stt') || modelId.includes('speech-to-text') || 
        modelId.includes('audio-to-text') || modelId.includes('whisper')
      );
    }
    
    supportsSpeechToTextModel(modelId: string): boolean {
      return this.getSupportedSpeechToTextModels().includes(modelId);
    }

    async startService(): Promise<boolean> {
      // Delegate to base provider's startService method if it exists
      if (typeof (this as any).startService === 'function') {
        return await (this as any).startService();
      }
      return true; // No-op for providers that don't need service management
    }

    async stopService(): Promise<boolean> {
      // Delegate to base provider's stopService method if it exists
      if (typeof (this as any).stopService === 'function') {
        return await (this as any).stopService();
      }
      return true; // No-op for providers that don't need service management
    }

    async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
      // Delegate to base provider's getServiceStatus method if it exists
      if (typeof (this as any).getServiceStatus === 'function') {
        return await (this as any).getServiceStatus();
      }
      return { running: true, healthy: true }; // Default for providers that don't manage services
    }
  };
}

// Alias for backward compatibility
export const withSpeechToTextProvider = withAudioToTextProvider;

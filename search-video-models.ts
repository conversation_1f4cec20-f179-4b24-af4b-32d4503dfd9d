import { ReplicateClient } from './src/media/clients/ReplicateClient';
import fetch from 'node-fetch';

async function searchVideoModels() {
  const client = new ReplicateClient({
    apiKey: process.env.REPLICATE_API_TOKEN!,
    discovery: { cacheDir: './cache' }
  });

  console.log('🔍 Searching for video and framepack models on Replicate...\n');

  try {
    // Search for framepack specifically
    console.log('1. Searching for "framepack":');
    const framepackResults = await client.searchModels('framepack');
    if (framepackResults.results.length > 0) {
      for (const model of framepackResults.results) {
        console.log(`   ✅ ${model.owner}/${model.name} - ${model.run_count} runs`);
        if (model.description) {
          console.log(`      ${model.description.substring(0, 100)}...`);
        }
      }
    } else {
      console.log('   ❌ No framepack models found');
    }

    // Search for image-to-video
    console.log('\n2. Searching for "image to video":');
    const videoResults = await client.searchModels('image to video');
    if (videoResults.results.length > 0) {
      for (const model of videoResults.results.slice(0, 5)) {
        console.log(`   🎥 ${model.owner}/${model.name} - ${model.run_count} runs`);
        if (model.description) {
          console.log(`      ${model.description.substring(0, 100)}...`);
        }
      }
    } else {
      console.log('   ❌ No image-to-video models found');
    }

    // Search for video generation
    console.log('\n3. Searching for "video generation":');
    const videoGenResults = await client.searchModels('video generation');
    if (videoGenResults.results.length > 0) {
      for (const model of videoGenResults.results.slice(0, 5)) {
        console.log(`   🎬 ${model.owner}/${model.name} - ${model.run_count} runs`);
        if (model.description) {
          console.log(`      ${model.description.substring(0, 100)}...`);
        }
      }
    }

    // Try to get the video-generation collection
    console.log('\n4. Checking video-generation collection:');
    try {
      const videoCollection = await client.getModelsByCollection('video-generation');
      if (videoCollection.length > 0) {
        console.log(`   Found ${videoCollection.length} models:`);
        for (const model of videoCollection.slice(0, 5)) {
          console.log(`   📚 ${model.owner}/${model.name}`);
        }
      }
    } catch (error) {
      console.log('   ❌ Video-generation collection not accessible');
    }

    // List all collections to see what's available
    console.log('\n5. Available collections:');
    const allModels = await client.listModels();
    console.log(`   Total models available: ${allModels.results.length} (first page)`);
    
    // Check if we can find collections endpoint
    try {
      // This will fail but let's see the actual available collections
      const collectionsUrl = 'https://api.replicate.com/v1/collections';
      const response = await fetch(collectionsUrl, {
        headers: { 'Authorization': `Bearer ${process.env.REPLICATE_API_TOKEN}` }
      });
      
      if (response.ok) {
        const collections = await response.json();
        console.log(`   Available collections (${collections.results.length}):`);
        for (const collection of collections.results.slice(0, 10)) {
          console.log(`   📚 ${collection.slug} - ${collection.name}`);
        }
      }
    } catch (error) {
      console.log('   ❌ Could not fetch collections');
    }

  } catch (error) {
    console.error('❌ Search failed:', error);
  }
}

searchVideoModels();

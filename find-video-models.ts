/**
 * Find Available Replicate Text-to-Video Models
 */

import { ReplicateProvider } from './packages/providers/src/remote/ReplicateProvider';

async function findVideoModels() {
  console.log('🎬 Finding Available Text-to-Video Models\n');

  const provider = new ReplicateProvider();
  await provider.configure({
    apiKey: process.env.REPLICATE_API_TOKEN || process.env.REPLICATE_API_KEY || 'r8_your_api_key_here'
  });

  console.log('📋 Available Text-to-Video Models:');
  
  const workingModels = [
    'minimax/video-01',
    'google/veo-3', 
    'kwaivgi/kling-v2.1',
    'stabilityai/stable-video-diffusion-img2vid-xt',
    'runwayml/gen-3-alpha-turbo'
  ];

  for (const modelId of workingModels) {
    try {
      console.log(`\n🔍 Testing: ${modelId}`);
      const metadata = await (provider as any).getModelMetadata(modelId);
      
      console.log(`   ✅ Available: ${metadata.name || modelId}`);
      console.log(`   📂 Category: ${metadata.category}`);
      console.log(`   🏃 Runs: ${metadata.runCount?.toLocaleString()}`);
      console.log(`   🔧 Parameters: ${Object.keys(metadata.parameters || {}).length}`);
      
      // Show key parameters
      const params = Object.keys(metadata.parameters || {}).slice(0, 3);
      if (params.length > 0) {
        console.log(`   📋 Key params: ${params.join(', ')}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Not available: ${error.message}`);
    }
  }

  console.log('\n🎯 Recommended models for testing:');
  console.log('   • minimax/video-01 - Fast, reliable text-to-video');
  console.log('   • google/veo-3 - High quality text-to-video');
  console.log('   • kwaivgi/kling-v2.1 - Image-to-video (if you have input image)');
}

findVideoModels().catch(console.error);

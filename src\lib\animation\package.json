{"name": "framepack-falai", "version": "1.0.0", "description": "FramePack integration with fal.ai API", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:example": "ts-node src/example.ts", "dev:example:url": "ts-node src/example-url.ts", "generate": "ts-node src/generate-video.ts", "generate-prompts": "ts-node src/generate-character-prompts.ts", "animate-portraits": "ts-node src/animate-character-portraits.ts", "character-animation": "ts-node src/cli.ts", "test": "echo \"Error: no test specified\" && exit 1", "test:api-key": "ts-node src/test-api-key.ts"}, "keywords": ["framepack", "fal.ai", "video", "generation", "api"], "author": "", "license": "MIT", "dependencies": {"@fal-ai/client": "^1.4.0", "@fal-ai/serverless-client": "^0.15.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.0", "axios": "^1.9.0", "dotenv": "^16.5.0", "express": "^4.18.2", "fs-extra": "^11.3.0", "gray-matter": "^4.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/express": "^4.17.21"}}
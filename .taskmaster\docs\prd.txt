# AutoMarket Media Transformation Platform - Clean Interface Architecture

## 🎯 CURRENT STATE

### ✅ Completed Foundation Work
- ✅ **Smart Asset Loading System** - Format-agnostic `AssetLoader.load()` with auto-detection and role mixins
- ✅ **Async Role Casting System** - Universal async role methods with intelligent FFmpeg integration
- ✅ **Clean Interface Pattern** - Universal asset loading with `model.transform()` for all formats
- ✅ **Role-Based Casting System** - Assets implement appropriate roles, models handle casting internally
- ✅ **FFmpeg Integration** - Automatic video-to-audio extraction for speech/audio roles
- ✅ **Docker Self-Management Pattern** - Working reference implementation with DockerComposeService
- ✅ **Basic Repository Structure** - Organized codebase with proper TypeScript setup
- ✅ **Testing Infrastructure** - Vitest setup with unit and integration test patterns
- ✅ **Working Provider Implementations** - Whisper STT and Chatterbox TTS with clean interfaces
- ✅ **Extensive Provider Reference Code** - FAL.ai and Replicate implementations exist
- ✅ **Multi-Format Support** - WAV, MP3, MP4, AVI, MOV, TXT all work with same clean interface

## 🔧 ARCHITECTURE ACHIEVED: SMART ASSET LOADING SYSTEM

### **Problem Solved**
Complex media type constructors requiring metadata we don't have, format-specific asset classes, and manual role conversion calls.

### **Solution: Smart Asset Loading with Automatic Format Detection and Role Assignment**

**Core Achievement**: Single loading interface for all formats with intelligent role mixin application
```typescript
// SMART ASSET LOADING - FORMAT AGNOSTIC
const videoAsset = AssetLoader.load('video.mp4');      // Auto-detects MP4, applies video+speech+audio roles
const audioAsset = AssetLoader.load('audio.mp3');      // Auto-detects MP3, applies speech+audio roles  
const textAsset = AssetLoader.load('document.txt');    // Auto-detects TXT, applies text role

// UNIVERSAL MODEL TRANSFORM - HANDLES ALL TYPES WITH ASYNC ROLE CASTING
const mp4Result = await model.transform(videoAsset);   // Video → Text (FFmpeg extracts audio automatically)
const mp3Result = await model.transform(audioAsset);   // Audio → Text (direct conversion)
const wavResult = await model.transform(wavAsset);     // Audio → Text (direct conversion)

// SAME MODEL, DIFFERENT PROVIDERS (FUTURE)
const dockerModel = await whisperDockerProvider.createModel('whisper');
const openaiModel = await openaiProvider.createModel('whisper');
const result1 = await dockerModel.transform(asset);    // Uses Docker
const result2 = await openaiModel.transform(asset);    // Uses OpenAI API
```

### **Key Principles Achieved**
1. **Smart Format-Agnostic Loading** - `AssetLoader.load()` auto-detects format and applies appropriate role mixins
2. **Async Role Casting with FFmpeg** - Video assets automatically extract audio using FFmpeg when cast to speech/audio
3. **No Metadata Pollution** - Only use data we actually have (file path, format detection)
4. **Format Agnostic** - Same interface whether WAV, MP3, MP4, AVI, MOV, TXT, or any other format
5. **Promise-Based Error Handling** - Unsupported inputs rejected with clear error messages
6. **Type Safety** - `SpeechInput = Speech | Audio | Video | (Asset & SpeechRole)`
7. **Intelligent Role Assignment** - Video files get video+audio+speech roles, audio files get audio+speech roles

## 🎯 SMART ASSET LOADING IMPLEMENTATION (ACHIEVED)

### **Smart Format Detection and Role Assignment**
```typescript
// SMART - Auto-detects format, applies appropriate role mixins
const videoAsset = AssetLoader.load('video.mp4');     // Auto-applies video+speech+audio roles
const audioAsset = AssetLoader.load('audio.mp3');     // Auto-applies speech+audio roles
const textAsset = AssetLoader.load('document.txt');   // Auto-applies text role

// LEGACY (still supported) - Format-specific loading
const audio = Audio.fromFile('audio.mp3');            // Still works, but less powerful
const video = Video.fromFile('video.mp4');            // Still works, but less powerful
```

### **Intelligent FFmpeg Integration**
```typescript
// When video assets are cast to speech/audio roles:
const videoAsset = AssetLoader.load('video.mp4');     // Loaded with all video capabilities

// These trigger automatic FFmpeg audio extraction:
const speech = await videoAsset.asSpeech();           // FFmpeg extracts audio → Speech object
const audio = await videoAsset.asAudio();             // FFmpeg extracts audio → Audio object
const video = await videoAsset.asVideo();             // Direct access → Video object

// Audio assets get direct conversion (no FFmpeg overhead):
const audioAsset = AssetLoader.load('audio.wav');
const speech2 = await audioAsset.asSpeech();          // Direct conversion → Speech object
```

### **Type-Safe Loading with Role Guarantees**
```typescript
// Generic loading with role guarantees
interface VideoAsset {
  asSpeech(): Promise<Speech>;
  asAudio(): Promise<Audio>;
  asVideo(): Promise<Video>;
}

const typedAsset = AssetLoader.fromFile<VideoAsset>('video.mp4');
// TypeScript guarantees these methods exist and return the right types
```

### **Async Role-Based Casting System**
```typescript
// Assets implement async role interfaces automatically
class VideoAssetWithMixins implements SpeechRole, AudioRole, VideoRole {
  async asSpeech(): Promise<Speech> {
    // Intelligent format detection → triggers FFmpeg for video formats
    if (this.isVideoFormat()) {
      return await this.extractSpeechFromVideo(); // Uses FFmpeg
    }
    return new Speech(this.data, this.sourceAsset); // Direct for audio
  }

  async asAudio(): Promise<Audio> {
    // Intelligent format detection → triggers FFmpeg for video formats  
    if (this.isVideoFormat()) {
      return await this.extractAudioFromVideo(); // Uses FFmpeg
    }
    return new Audio(this.data, this.sourceAsset); // Direct for audio
  }

  async asVideo(): Promise<Video> {
    return new Video(this.data, this.sourceAsset); // Direct access
  }

  canPlaySpeechRole(): boolean {
    return this.isValid(); // Any valid media can potentially have speech
  }
}
```

### **Model Input Flexibility with Async Casting**
```typescript
// Models accept union types with automatic async casting
abstract class SpeechToTextModel {
  abstract transform(input: SpeechInput, options?: SpeechToTextOptions): Promise<Text>;
}

// SpeechInput = Speech | Audio | Video | (Asset & SpeechRole)
// Model calls await castToSpeech(input) internally which calls await input.asSpeech()
```

### **Smart Usage Pattern**
```typescript
// STEP 1: Load media with smart auto-detection
const videoAsset = AssetLoader.load('file.mp4');  // Auto-detects format, applies role mixins
const audioAsset = AssetLoader.load('file.mp3');  // Auto-detects format, applies role mixins

// STEP 2: Transform with universal interface (FFmpeg happens automatically)
const result1 = await model.transform(videoAsset); // Model calls asSpeech() → FFmpeg extraction
const result2 = await model.transform(audioAsset); // Model calls asSpeech() → direct conversion

// STEP 3: Error handling via promise rejection
try {
  const textAsset = AssetLoader.load('document.txt');
  await model.transform(textAsset); // Throws: missing SpeechRole capability
} catch (error) {
  // Clear error message about unsupported input
}
```

## � TTS DUAL-SIGNATURE PATTERN

### **Problem: Voice Cloning as Options Breaks Clean Interface**
Previous approach mixed different operation modes through options:
```typescript
// INCONSISTENT - Basic TTS and voice cloning look the same
await model.transform(text, { voice: 'default' });
await model.transform(text, { voiceFile: 'voice.wav' }); // Different operation, same signature
```

### **Solution: Dual Transform Signatures - Same Pattern as Whisper STT**
Voice cloning is a fundamentally different operation that takes different inputs:

```typescript
// CONSISTENT WITH WHISPER PATTERN
// Whisper STT: Multiple input types, same method name
await whisperModel.transform(audio);     // Audio → Text
await whisperModel.transform(video);     // Video → Text (extracts audio)

// TTS: Multiple input combinations, same method name  
await ttsModel.transform(text);                    // Text → Speech (basic TTS)
await ttsModel.transform(text, voiceAudio);        // Text + Audio → Speech (voice cloning)
```

### **Key Benefits**
1. **Type Safety** - Compiler knows exactly what inputs are expected for each mode
2. **Consistency** - Same pattern as Whisper's multi-input handling  
3. **Separation of Concerns** - Voice cloning is clearly a different operation, not just an option
4. **Discoverability** - Developers can see all available transform signatures
5. **Clean Interface** - No magic options that change behavior dramatically

### **Implementation Pattern**
```typescript
abstract class TextToAudioModel extends Model {
  // Basic text-to-audio transformation (includes text-to-speech)
  abstract transform(input: Text | (Asset & TextRole)): Promise<Speech>;

  // Voice cloning transformation - accepts Speech (which Audio can play the role of)
  abstract transform(text: Text | (Asset & TextRole), voiceSpeech: Speech | (Asset & SpeechRole)): Promise<Speech>;
}

// Usage Examples with Smart Asset Loading
const text = AssetLoader.load('script.txt');       // Auto-detects text format
const voiceAudio = AssetLoader.load('voice.wav');  // Auto-detects audio, applies speech role

// Basic TTS
const basicSpeech = await ttsModel.transform(text);

// Voice cloning - voiceAudio automatically has asSpeech() due to smart loading
const clonedSpeech = await ttsModel.transform(text, voiceAudio); // Async role casting happens automatically

// Basic TTS
const basicSpeech = await model.transform(text);

// Voice cloning - clearly different signature
const clonedSpeech = await model.transform(text, voiceSpeech);
```

## �🏗️ SEPARATION OF CONCERNS ARCHITECTURE

### **Current Problem: Monolithic Services**
The existing `ChatterboxTTSDockerService` and `WhisperSTTService` mix multiple concerns:
- Docker management + API calls + business logic + MediaTransformer interface
- Not DRY, not extensible, not reusable

### **Solution: Clean Layer Separation**

#### **Layer 1: Service Management** (Infrastructure)
```typescript
// Generic Docker operations (KEEP - already DRY)
DockerComposeService

// Service-specific Docker configs (EXTRACT from monoliths)
ChatterboxDockerService  // Just Docker + health checks
WhisperDockerService     // Just Docker + health checks
```

#### **Layer 2: API Clients** (Pure Communication)
```typescript
// Pure HTTP API clients (EXTRACT from current monoliths)
ChatterboxAPIClient      // Just HTTP calls to Chatterbox API
WhisperAPIClient         // Just HTTP calls to Whisper API
OpenAIAPIClient          // HTTP calls to OpenAI API
ReplicateAPIClient       // HTTP calls to Replicate API
ElevenLabsAPIClient      // HTTP calls to ElevenLabs API
```

#### **Layer 3: Smart Asset System with Dynamic Role Mixins** (Business Logic)
```typescript
// Smart Asset System with Dynamic Role Assignment
AssetLoader (smart factory)
├── Format Detection Registry
│   ├── Audio formats: MP3, WAV, FLAC → SpeechRole + AudioRole
│   ├── Video formats: MP4, AVI, MOV → VideoRole + SpeechRole + AudioRole (with FFmpeg)
│   ├── Text formats: TXT, MD, JSON → TextRole
│   └── Extensible for new formats
├── Dynamic Mixin Application
│   ├── withSpeechRole(Asset) → Asset & SpeechRole (with FFmpeg for video)
│   ├── withAudioRole(Asset) → Asset & AudioRole (with FFmpeg for video)
│   ├── withVideoRole(Asset) → Asset & VideoRole
│   └── withTextRole(Asset) → Asset & TextRole
└── Smart Loading Interface
    ├── AssetLoader.load(filePath) → Auto-detects format, applies appropriate mixins
    ├── AssetLoader.fromFile<T>(filePath) → Type-safe loading with role guarantees
    └── AssetLoader.fromBuffer(buffer, format) → Buffer-based loading with format specification

// Legacy Asset Types (still supported but superseded by smart loading)
├── MP3Asset extends withSpeechRole(withAudioRole(Asset))      // ⚠️ Legacy
├── MP4Asset extends withSpeechRole(withAudioRole(withVideoRole(Asset))) // ⚠️ Legacy
├── WAVAsset extends withSpeechRole(withAudioRole(Asset))      // ⚠️ Legacy
└── TextAsset extends withTextRole(Asset)                     // ⚠️ Legacy

// Model Hierarchy with Asset-Role Inputs/Outputs
Model (abstract base)
├── TextToAudioModel (abstract, includes text-to-speech functionality)
│   ├── ChatterboxTTSModel (concrete implementation)
│   ├── ChatterboxDockerModel (concrete implementation)
│   └── ReplicateTextToAudioModel (concrete implementation)
└── AudioToTextModel (abstract)
    └── SpeechToTextModel (abstract, extends AudioToTextModel)
        └── WhisperSTTModel (concrete implementation)

// Clean Model Interfaces with Autocasting
SpeechToTextModel.transform(input: Speech | (Asset & SpeechRole)): Promise<Text>
TextToSpeechModel.transform(input: Text | (Asset & TextRole)): Promise<Speech>

// Type-Safe Usage with Autocasting
const mp4Asset = MP4Asset.fromFile('video.mp4');  // Has SpeechRole
const transcript = await whisperModel.transform(mp4Asset);  // Autocasts to Speech
const newSpeech = await chatterboxModel.transform(transcript);  // Text → Speech
```

#### **Layer 4: Provider Role Interface Architecture** (Model Management + Infrastructure)

**Core Principle**: Providers implement capability interfaces with required service management methods and delegate to services for infrastructure.

```typescript
// Provider Role Interfaces - IMPLEMENTED ARCHITECTURE
export interface SpeechToTextProvider {
  /**
   * Create a speech-to-text model instance
   */
  createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel>;

  /**
   * Get supported speech-to-text models
   */
  getSupportedSpeechToTextModels(): string[];

  /**
   * Check if provider supports a specific STT model
   */
  supportsSpeechToTextModel(modelId: string): boolean;

  /**
   * Start the underlying service (no-op for remote providers, functional for Docker providers)
   */
  startService(): Promise<boolean>;

  /**
   * Stop the underlying service (no-op for remote providers, functional for Docker providers)
   */
  stopService(): Promise<boolean>;

  /**
   * Get service status (no-op for remote providers, functional for Docker providers)
   */
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

export interface TextToAudioProvider {
  /**
   * Create a text-to-audio model instance
   */
  createTextToAudioModel(modelId: string): Promise<TextToAudioModel>;

  /**
   * Get supported text-to-audio models
   */
  getSupportedTextToAudioModels(): string[];

  /**
   * Check if provider supports a specific TTS model
   */
  supportsTextToAudioModel(modelId: string): boolean;

  /**
   * Start the underlying service (no-op for remote providers, functional for Docker providers)
   */
  startService(): Promise<boolean>;

  /**
   * Stop the underlying service (no-op for remote providers, functional for Docker providers)
   */
  stopService(): Promise<boolean>;

  /**
   * Get service status (no-op for remote providers, functional for Docker providers)
   */
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

// ACTUAL IMPLEMENTATION - Direct Interface Implementation
export class WhisperDockerProvider extends LocalProvider implements SpeechToTextProvider {
  readonly id = 'whisper-docker';
  readonly name = 'Whisper Docker Provider';

  private dockerService?: WhisperDockerService;
  private apiClient?: WhisperAPIClient;

  // Core interface methods
  async createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel> {
    return this.createModel(modelId);
  }

  getSupportedSpeechToTextModels(): string[] {
    return this.getAvailableModels();
  }

  supportsSpeechToTextModel(modelId: string): boolean {
    return this.supportsModel(modelId);
  }

  // Service management methods (required, not optional)
  async startService(): Promise<boolean> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const started: boolean = await dockerService.startService();

    if (started) {
      const healthy: boolean = await dockerService.waitForHealthy(30000);
      return healthy;
    }

    return false;
  }

  async stopService(): Promise<boolean> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    return await dockerService.stopService();
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const status = await dockerService.getServiceStatus();

    return {
      running: status.running || false,
      healthy: status.health === 'healthy'
    };
  }

  // Implementation details
  async createModel(modelId: string): Promise<SpeechToTextModel> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const apiClient: WhisperAPIClient = await this.getAPIClient();

    const model: WhisperDockerModel = new WhisperDockerModel({
      dockerService,
      apiClient
    });

    return model;
  }
}

export class ChatterboxDockerProvider extends LocalProvider implements TextToAudioProvider {
  readonly id = 'chatterbox-docker';
  readonly name = 'Chatterbox Docker Provider';

  // Same pattern as WhisperDockerProvider
  // Implements all TextToAudioProvider methods including service management
}
```

**Key Architecture Points - IMPLEMENTED:**
1. **Required Service Management**: Service methods are required (not optional) in provider interfaces
2. **Direct Interface Implementation**: Providers directly implement role interfaces without mixins
3. **Type Safety**: Full TypeScript compliance with explicit type annotations
4. **Clean Delegation**: Providers delegate to services, services manage infrastructure
5. **Interface Compliance**: Docker providers implement service methods, remote providers can implement as no-ops
6. **Separation of Concerns**: Provider = Infrastructure + Model Creation, Model = Pure Transformation
7. **Testability**: Models can be unit tested independently of Docker infrastructure

#### **Layer 5: Direct Provider Usage with Type Safety** (Coordination)

**IMPLEMENTED PATTERN**: Direct provider instantiation with full service lifecycle management

```typescript
// ACTUAL IMPLEMENTATION - Integration Test Pattern
async function runIntegrationTest(): Promise<void> {
  // Create provider with explicit typing
  const provider: SpeechToTextProvider = new WhisperDockerProvider();

  // Service lifecycle management
  const serviceStarted: boolean = await provider.startService();
  assert(serviceStarted, 'Docker service started');

  const status: { running: boolean; healthy: boolean; error?: string } = await provider.getServiceStatus();
  assert(status.running, 'Docker service is running');
  assert(status.healthy, 'Docker service is healthy');

  // Model creation using interface method
  const model: SpeechToTextModel = await provider.createSpeechToTextModel('whisper-stt');
  const isAvailable: boolean = await model.isAvailable();
  assert(isAvailable, 'Model is available');

  // Smart Asset Loading - Auto-detects formats and applies role mixins
  const wavAudio = AssetLoader.load(wavPath);        // Auto-detects WAV, applies speech+audio roles
  const wavResult: Text = await model.transform(wavAudio);
  assert(!!wavResult.content, 'WAV result has content property');

  const mp3Audio = AssetLoader.load(mp3Path);        // Auto-detects MP3, applies speech+audio roles
  const mp3Result: Text = await model.transform(mp3Audio);
  assert(!!mp3Result.content, 'MP3 result has content property');

  const mp4Video = AssetLoader.load(mp4Path);        // Auto-detects MP4, applies video+speech+audio roles
  const mp4Result: Text = await model.transform(mp4Video); // FFmpeg extraction happens automatically
  assert(!!mp4Result.content, 'MP4 result has content property');

  // Service cleanup
  const stopped: boolean = await provider.stopService();
  assert(stopped, 'Service is stopped');
}

// TTS Implementation follows same pattern
async function runTTSTest(): Promise<void> {
  const provider: TextToSpeechProvider = new ChatterboxDockerProvider();

  await provider.startService();
  const model: TextToSpeechModel = await provider.createTextToSpeechModel('chatterbox-tts');

  // Basic TTS - smart asset loading
  const textFromString: Text = Text.fromString('Hello, this is a test');
  const stringResult: Speech = await model.transform(textFromString);

  const textFromFile = AssetLoader.load(testTextPath);     // Auto-detects text format, applies text role
  const fileResult: Speech = await model.transform(textFromFile);

  // Voice cloning TTS - smart asset loading with automatic async role casting
  const voiceAudio = AssetLoader.load('voice-sample.wav'); // Auto-detects WAV, applies speech+audio roles
  const clonedResult: Speech = await model.transform(textFromString, voiceAudio); // Async asSpeech() called automatically

  await provider.stopService();
}
```

## 🎯 FAL.AI INTEGRATION ARCHITECTURE (NEW)

### **Multi-Capability Provider System with Model-Specific Interfaces**

**Core Achievement**: Single fal.ai provider implementing multiple XProvider interfaces, with each model exposing its own specialized API while sharing common infrastructure.

### **XProvider → XtoYModel → Specialized Model Pattern**

**The Pattern**: fal.ai supports multiple transformation types through one provider, but each model needs its own specialized interface:

```typescript
// 1. ONE PROVIDER, MULTIPLE CAPABILITIES
export class FalAiProvider implements 
  TextToImageProvider,
  ImageToVideoProvider, 
  TextToVideoProvider,
  VideoToVideoProvider {
  
  constructor(private falAdapter: FalAiAdapter) {}
  
  // TextToImageProvider interface
  async createTextToImageModel(modelId: string): Promise<TextToImageModel> {
    switch(modelId) {
      case 'flux-pro':
        return new FalFluxProModel(this.falAdapter, modelId);
      case 'flux-dev':
        return new FalFluxDevModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported text-to-image model: ${modelId}`);
    }
  }
  
  // ImageToVideoProvider interface
  async createImageToVideoModel(modelId: string): Promise<ImageToVideoModel> {
    switch(modelId) {
      case 'framepack':
        return new FalFramePackModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported image-to-video model: ${modelId}`);
    }
  }
  
  // TextToVideoProvider interface
  async createTextToVideoModel(modelId: string): Promise<TextToVideoModel> {
    switch(modelId) {
      case 'runway-gen3':
        return new FalRunwayGen3Model(this.falAdapter, modelId);
      case 'stable-video-diffusion':
        return new FalStableVideoModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported text-to-video model: ${modelId}`);
    }
  }
  
  // VideoToVideoProvider interface
  async createVideoToVideoModel(modelId: string): Promise<VideoToVideoModel> {
    switch(modelId) {
      case 'face-swap':
        return new FalFaceSwapModel(this.falAdapter, modelId);
      case 'video-enhance':
        return new FalVideoEnhanceModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported video-to-video model: ${modelId}`);
    }
  }
}
```

### **Model-Specific Interfaces with Shared Infrastructure**

**Key Innovation**: Each fal.ai model gets its own specialized interface that matches its actual API, while sharing common infrastructure:

```typescript
// 2. SPECIALIZED MODEL IMPLEMENTATIONS
export class FalFramePackModel extends ImageToVideoModel {
  constructor(private falAdapter: FalAiAdapter, private modelId: string) {
    super({
      id: 'fal-framepack',
      name: 'FAL FramePack Animation',
      description: 'Animate static images with AI',
      version: '1.0.0',
      provider: 'fal.ai',
      capabilities: ['image-to-video', 'animation'],
      inputTypes: ['image'],
      outputTypes: ['video']
    });
  }
  
  // Standard transform method - unified interface
  async transform(image: ImageInput, options: {
    prompt: string;             // Required for FramePack
    num_frames?: number;        // 50-300, default 150
    fps?: number;              // 15-60, default 30
    guidance_scale?: number;   // 1-20, default 7.5
    video_length?: number;     // 2-10 seconds, default 5
    aspect_ratio?: '16:9' | '9:16' | '1:1';
    teacache?: boolean;        // Performance optimization
  }): Promise<Video> {
    const imageAsset = await castToImage(image);
    
    // Convert to fal.ai format
    const requestData = {
      prompt: options.prompt,
      image_url: await this.uploadAsset(imageAsset),
      num_frames: options.num_frames || 150,
      fps: options.fps || 30,
      guidance_scale: options.guidance_scale || 7.5,
      video_length: options.video_length || 5,
      aspect_ratio: options.aspect_ratio || '16:9',
      teacache: options.teacache !== false
    };
    
    // Call fal.ai API through adapter
    const result = await this.falAdapter.invoke('framepack', requestData);
    
    // Convert back to our Video asset
    return Video.fromUrl(result.video.url);
  }
}

export class FalFaceSwapModel extends VideoToVideoModel {
  // Standard transform method - unified VideoToVideoModel interface
  async transform(
    baseVideo: VideoInput, 
    overlayVideos: VideoInput | VideoInput[], 
    options: {
      face_restore?: boolean;
      background_enhance?: boolean;
      detection_threshold?: number;
    } = {}
  ): Promise<VideoCompositionResult> {
    // For face swap, overlayVideos should be the source image/video
    if (Array.isArray(overlayVideos)) {
      throw new Error('Face swap only supports single source image');
    }
    
    const sourceAsset = await castToImage(overlayVideos); // Source face
    const targetAsset = await castToVideo(baseVideo);     // Target video
    
    const requestData = {
      source_image: await this.uploadAsset(sourceAsset),
      target_video: await this.uploadAsset(targetAsset),
      face_restore: options.face_restore !== false,
      background_enhance: options.background_enhance !== false,
      detection_threshold: options.detection_threshold || 0.6
    };
    
    const result = await this.falAdapter.invoke('face-swap', requestData);
    const resultVideo = Video.fromUrl(result.video.url);
    
    return {
      composedVideo: resultVideo,
      metadata: {
        duration: result.duration || 0,
        resolution: result.resolution || '',
        aspectRatio: result.aspect_ratio || '',
        framerate: result.fps || 0,
        baseVideoInfo: { 
          duration: targetAsset.duration || 0, 
          resolution: targetAsset.resolution || '' 
        },
        overlayInfo: { 
          count: 1, 
          overlays: [{
            index: 0,
            startTime: 0,
            duration: result.duration || 0,
            position: 'face-swap',
            finalSize: { width: 0, height: 0 }
          }]
        }
      }
    };
  }
}

export class FalRunwayGen3Model extends TextToVideoModel {
  // Standard transform method - unified TextToVideoModel interface
  async transform(text: TextInput, options: {
    duration?: number;              // 2-10 seconds
    aspect_ratio?: '16:9' | '9:16' | '1:1';
    resolution?: '480p' | '720p' | '1080p';
    motion_strength?: number;       // 0.0-1.0
    camera_motion?: 'static' | 'pan' | 'zoom' | 'rotate';
    seed?: number;                  // For reproducibility
  } = {}): Promise<Video> {
    const textAsset = await castToText(text);
    
    const requestData = {
      prompt: textAsset.content,
      duration: options.duration || 5,
      aspect_ratio: options.aspect_ratio || '16:9',
      resolution: options.resolution || '720p',
      motion_strength: options.motion_strength || 0.5,
      camera_motion: options.camera_motion || 'static',
      seed: options.seed
    };
    
    const result = await this.falAdapter.invoke('runway-gen3', requestData);
    return Video.fromUrl(result.video.url);
  }
}
```

### **Usage Patterns - Type-Safe Model-Specific APIs**

**Clients use the unified transform() interface with model-specific options:**

```typescript
// 1. IMAGE ANIMATION with FramePack-specific options
const provider = ProviderFactory.getImageToVideoProvider('fal-ai');
const framePackModel = await provider.createImageToVideoModel('framepack');

const image = AssetLoader.load('photo.jpg');
const animatedVideo = await framePackModel.transform(image, {
  prompt: "dancing cat",
  num_frames: 150,
  fps: 30,
  aspect_ratio: '16:9',
  guidance_scale: 8.0,
  teacache: true
});

// 2. FACE SWAPPING with FaceSwap-specific options
const videoProvider = ProviderFactory.getVideoToVideoProvider('fal-ai');
const faceSwapModel = await videoProvider.createVideoToVideoModel('face-swap');

const sourceImage = AssetLoader.load('face.jpg');     // Source face (overlay)
const targetVideo = AssetLoader.load('video.mp4');    // Target video (base)
const result = await faceSwapModel.transform(targetVideo, sourceImage, {
  face_restore: true,
  background_enhance: true,
  detection_threshold: 0.7
});
const swappedVideo = result.composedVideo;

// 3. TEXT-TO-VIDEO with Runway-specific options
const textVideoProvider = ProviderFactory.getTextToVideoProvider('fal-ai');
const runwayModel = await textVideoProvider.createTextToVideoModel('runway-gen3');

const text = Text.fromString("a cat playing piano");
const generatedVideo = await runwayModel.transform(text, {
  duration: 8,
  aspect_ratio: '16:9',
  resolution: '1080p',
  motion_strength: 0.8,
  camera_motion: 'pan'
});

// 4. UNIVERSAL PATTERN - same transform() method across all models
const models = [framePackModel, faceSwapModel, runwayModel];
for (const model of models) {
  const result = await model.transform(input, modelSpecificOptions);
  // Each model interprets options according to its capabilities
}
```

### **Key Architecture Benefits**

1. **✅ Model-Specific APIs** - Each model exposes its actual fal.ai API parameters and methods
2. **✅ Type Safety** - Full TypeScript support with model-specific parameter validation
3. **✅ Shared Infrastructure** - All models share authentication, rate limiting, error handling via FalAiAdapter
4. **✅ Provider Abstraction** - Client code doesn't know it's using fal.ai - just asks for capabilities
5. **✅ Extensibility** - Easy to add new fal.ai models by implementing new specialized model classes
6. **✅ Standard Compliance** - All models still implement the base XtoYModel interface for compatibility
7. **✅ Discovery** - Provider factory enables capability-based discovery and routing

### **Multi-Provider Ecosystem**

**The same pattern works for multiple providers:**

```typescript
// Client asks for capability, gets best available provider
const imageProvider = ProviderFactory.getTextToImageProvider('fal-ai');
const imageModel = await imageProvider.createTextToImageModel('flux-pro');

const videoProvider = ProviderFactory.getTextToVideoProvider('runway-direct'); // Different provider
const videoModel = await videoProvider.createTextToVideoModel('gen-3-turbo');

// Same interface, different providers, model-specific APIs
```

## 🚀 SMART ASSET LOADING ACHIEVEMENTS (NEW)

### **Revolutionary Format-Agnostic Architecture**

**Before (Format-Specific):**
```typescript
// Required format-specific knowledge and classes
const mp4Asset = MP4Asset.fromFile('video.mp4');     // ❌ Format-specific
const mp3Asset = MP3Asset.fromFile('audio.mp3');     // ❌ Format-specific  
const wavAsset = WAVAsset.fromFile('audio.wav');     // ❌ Format-specific
```

**After (Smart Auto-Detection):**
```typescript
// Universal format-agnostic loading
const videoAsset = AssetLoader.load('video.mp4');    // ✅ Auto-detects MP4, applies video+speech+audio roles
const audioAsset = AssetLoader.load('audio.mp3');    // ✅ Auto-detects MP3, applies speech+audio roles
const wavAsset = AssetLoader.load('audio.wav');      // ✅ Auto-detects WAV, applies speech+audio roles
```

### **Intelligent FFmpeg Integration**

**Video Files → Audio Extraction:**
```typescript
const videoAsset = AssetLoader.load('video.mp4');

// These automatically trigger FFmpeg audio extraction:
const speech = await videoAsset.asSpeech();          // FFmpeg: MP4 → WAV audio → Speech
const audio = await videoAsset.asAudio();            // FFmpeg: MP4 → WAV audio → Audio
const video = await videoAsset.asVideo();            // Direct: MP4 → Video (no FFmpeg)
```

**Audio Files → Direct Conversion:**
```typescript
const audioAsset = AssetLoader.load('audio.wav');

// These use direct conversion (no FFmpeg overhead):
const speech = await audioAsset.asSpeech();          // Direct: WAV → Speech
const audio = await audioAsset.asAudio();            // Direct: WAV → Audio
```

### **Integration Test Results**

**Whisper STT (Video Processing):**
```
🎬 Testing MP4 Video...
[SpeechRole Mixin] asSpeech() called on format: mp4
[SpeechRole Mixin] Video format detected, extracting speech from video using FFmpeg...
[FFmpegService] extractAudio called with format: mp4, buffer size: 96365415 bytes
[FFmpegService] Audio extraction successful, output size: 29544998 bytes
📝 MP4 result: "300 million job massacre. Goldman Sachs AI displacement analysis..."
```

**Chatterbox TTS (Voice Cloning):**
```
🎵 Testing voice cloning with Audio input...
[SpeechRole Mixin] asSpeech() called on format: wav
[SpeechRole Mixin] Audio format detected, direct conversion to Speech
✅ Voice cloning result exists and has audio data
```

### **Key Benefits Delivered**

1. **✅ Format Agnostic** - Same loading interface for all media formats
2. **✅ Intelligent Processing** - Automatic FFmpeg integration for video files
3. **✅ Performance Optimized** - Direct conversion for audio files (no unnecessary FFmpeg)
4. **✅ Type Safe** - Generic loading with role guarantees via TypeScript
5. **✅ Extensible** - Easy to add new formats by updating the format registry
6. **✅ Backward Compatible** - Legacy format-specific classes still work
7. **✅ Clean API** - Single loading method replaces dozens of format-specific classes

### **Supported Formats & Auto-Applied Roles**

| Format Category | Extensions | Roles Applied | FFmpeg Used |
|-----------------|------------|---------------|-------------|
| **Video** | MP4, AVI, MOV, WMV, FLV, WEBM, MKV | Video + Audio + Speech | ✅ For audio extraction |
| **Audio** | MP3, WAV, FLAC, OGG | Audio + Speech | ❌ Direct conversion |
| **Text** | TXT, MD, JSON, XML, HTML, CSS, JS, TS | Text | ❌ Direct conversion |

### **Migration Path**

**Legacy Code (still works):**
```typescript
const mp4Asset = MP4Asset.fromFile('video.mp4');     // ⚠️ Legacy but functional
const result = await model.transform(mp4Asset);
```

**Modern Code (recommended):**
```typescript
const videoAsset = AssetLoader.load('video.mp4');    // ✅ Smart, format-agnostic
const result = await model.transform(videoAsset);    // Same result, better architecture
```

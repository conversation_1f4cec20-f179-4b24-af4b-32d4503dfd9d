# AutoMarket Media Transformation Platform - Provider/Model/Service/Client Architecture

## 🎯 CURRENT STATE - PRODUCTION ARCHITECTURE

### ✅ Completed Production Systems
- ✅ **Model-Registry System** - Central registry with dynamic provider-model relationships and self-registration
- ✅ **Capability-Driven Provider Discovery** - MediaProvider interface with capability enumeration and automatic selection
- ✅ **Multi-Provider Architecture** - Local Docker services + Remote API providers unified under single interface
- ✅ **Service Layer Separation** - Clean separation: Providers → Services → API Clients → Docker/HTTP
- ✅ **Smart Asset Loading System** - Format-agnostic `AssetLoader.load()` with auto-detection and role mixins
- ✅ **Production Provider Implementations** - WhisperDockerProvider, ChatterboxProvider, FAL.ai, Replicate, Creatify
- ✅ **Factory Pattern Infrastructure** - ProviderFactory for provider selection, ClientFactory for API client creation
- ✅ **Comprehensive Testing** - Integration tests with real Docker services and API providers
- ✅ **Type-Safe Registry Pattern** - Thread-safe registries with validation and error handling
- ✅ **Multi-Modal Support** - Single providers supporting multiple capabilities (text→image, image→video, etc.)
- ✅ **Production-Ready Infrastructure** - Docker self-management, health monitoring, graceful error handling

## 🏗️ PRODUCTION ARCHITECTURE: MODEL-REGISTRY SYSTEM

### **Core Achievement: Dynamic Provider-Model Relationships**

The system has evolved beyond static provider implementations to a sophisticated registry-based architecture that enables dynamic discovery, registration, and selection of providers and models.

**Central Pattern**: Models self-register with providers through descriptors, enabling flexible provider-model relationships without hardcoded dependencies.

```typescript
// CURRENT PRODUCTION PATTERN - Model-Registry System
const modelRegistry = new DefaultModelRegistry();
const providerRegistry = new DefaultProviderRegistry();

// 1. Models self-register with providers
modelRegistry.register({
  modelId: 'whisper-stt',
  providerId: 'whisper-docker',
  name: 'Whisper Speech-to-Text',
  implementation: WhisperSTTModel,
  inputSchema: { type: 'Speech | Audio | Video' },
  outputSchema: { type: 'Text' }
});

// 2. Providers register themselves
providerRegistry.register(new WhisperDockerProvider());

// 3. Dynamic provider-model lookup
const provider = getProvider('whisper-docker');
const model = await provider.getModel('whisper-stt');
const result = await model.transform(audioInput);
```

### **Key Architectural Principles Achieved**

1. **Dynamic Registration** - Models and providers register at runtime without hardcoded dependencies
2. **Capability-Driven Discovery** - Providers declare capabilities (TEXT_TO_SPEECH, IMAGE_GENERATION, etc.)
3. **Multi-Provider Support** - Same model can be provided by multiple providers (Docker, OpenAI, etc.)
4. **Type-Safe Validation** - Registry validates model descriptors and provider compatibility
5. **Thread-Safe Operations** - Concurrent registration and lookup with proper locking
6. **Automatic Provider Selection** - Factory patterns for capability-based provider discovery
7. **Service Layer Isolation** - Clean separation of providers, services, clients, and models

## 🎯 PRODUCTION PROVIDER ARCHITECTURE

### **Layer 1: MediaProvider Interface** (Capability Declaration)

**Achievement**: Unified interface for all providers (local Docker + remote APIs) with capability-driven discovery.

```typescript
// PRODUCTION INTERFACE - All providers implement this
interface MediaProvider {
  readonly id: string;
  readonly name: string;
  readonly type: ProviderType;                    // 'local' | 'remote'
  readonly capabilities: MediaCapability[];      // What this provider can do
  readonly models: ProviderModel[];              // Available models per capability
  
  configure(config: ProviderConfig): Promise<void>;
  isAvailable(): Promise<boolean>;
  getModelsForCapability(capability: MediaCapability): ProviderModel[];
  generate(request: GenerationRequest): Promise<GenerationResult>;
}

// PRODUCTION ENUM - Extensive capability coverage
enum MediaCapability {
  TEXT_TO_SPEECH = 'text-to-speech',
  SPEECH_TO_TEXT = 'speech-to-text',
  IMAGE_GENERATION = 'image-generation',
  VIDEO_ANIMATION = 'video-animation',
  VIDEO_GENERATION = 'video-generation',
  AVATAR_GENERATION = 'avatar-generation',
  VOICE_CLONING = 'voice-cloning',
  // ... 20+ capabilities defined
}
```

### **Layer 2: Provider Hierarchy** (Implementation Patterns)

**Local Provider Pattern** (Docker-based services):
```typescript
// PRODUCTION IMPLEMENTATION - WhisperDockerProvider
export class WhisperDockerProvider extends LocalProvider {
  readonly id = 'whisper-docker';
  readonly capabilities = [MediaCapability.SPEECH_TO_TEXT];
  
  protected async getDockerService(): Promise<WhisperDockerService> {
    return new WhisperDockerService();
  }
  
  protected async getAPIClient(): Promise<WhisperAPIClient> {
    return new WhisperAPIClient(await this.getBaseUrl());
  }
  
  async createModel(modelId: string): Promise<WhisperSTTModel> {
    const context = await this.getProviderContext();
    return new WhisperSTTModel(context);
  }
}
```

**Remote Provider Pattern** (API-based services):
```typescript
// PRODUCTION IMPLEMENTATION - FAL.ai Provider
export class FalAiAdapter implements MediaProvider {
  readonly id = 'fal-ai';
  readonly capabilities = [
    MediaCapability.IMAGE_GENERATION,
    MediaCapability.VIDEO_ANIMATION,
    MediaCapability.VIDEO_GENERATION
  ];
  
  async configure(config: ProviderConfig): Promise<void> {
    this.apiClient = new FalAiClient(config.apiKey);
  }
  
  async generate(request: GenerationRequest): Promise<GenerationResult> {
    // Capability-based model selection and execution
    const model = this.selectModelForCapability(request.capability);
    return await this.executeGeneration(model, request);
  }
}
```

### **Layer 3: Service Infrastructure** (Clean Separation)

**Docker Service Layer**:
```typescript
// PRODUCTION PATTERN - Service manages Docker lifecycle only
export class WhisperDockerService extends DockerComposeService {
  protected getServiceName(): string { return 'whisper'; }
  protected getDockerComposePath(): string { return './services/whisper'; }
  
  async isServiceHealthy(): Promise<boolean> {
    return await this.checkContainerHealth('whisper');
  }
}
```

**API Client Layer**:
```typescript
// PRODUCTION PATTERN - Client handles HTTP communication only
export class WhisperAPIClient {
  async transcribeAudio(audioData: Buffer): Promise<TranscriptionResult> {
    const response = await fetch(`${this.baseUrl}/transcribe`, {
      method: 'POST',
      body: audioData,
      headers: { 'Content-Type': 'audio/wav' }
    });
    return await response.json();
  }
}
```

### **Layer 4: Model Implementation** (Pure Transformation)

**Model Pattern** (Business logic only):
```typescript
// PRODUCTION PATTERN - Model focuses on transformation logic
export class WhisperSTTModel implements SpeechToTextModel {
  constructor(private context: { dockerService: WhisperDockerService; apiClient: WhisperAPIClient }) {}
  
  async transform(input: SpeechInput): Promise<Text> {
    // 1. Ensure infrastructure is ready
    await this.ensureServiceRunning();
    
    // 2. Handle input role casting (Video → Audio extraction)
    const speech = await this.castToSpeech(input);
    
    // 3. Execute pure transformation
    const result = await this.context.apiClient.transcribeAudio(speech.data);
    
    // 4. Return typed result
    return new Text(result.transcript, result.language, result.confidence);
  }
}
```
    if (this.isVideoFormat()) {
      return await this.extractSpeechFromVideo(); // Uses FFmpeg
    }
    return new Speech(this.data, this.sourceAsset); // Direct for audio
  }

  async asAudio(): Promise<Audio> {
    // Intelligent format detection → triggers FFmpeg for video formats  
    if (this.isVideoFormat()) {
      return await this.extractAudioFromVideo(); // Uses FFmpeg
    }
    return new Audio(this.data, this.sourceAsset); // Direct for audio
  }

  async asVideo(): Promise<Video> {
    return new Video(this.data, this.sourceAsset); // Direct access
  }

  canPlaySpeechRole(): boolean {
    return this.isValid(); // Any valid media can potentially have speech
  }
}
```

### **Model Input Flexibility with Async Casting**
```typescript
// Models accept union types with automatic async casting
abstract class SpeechToTextModel {
  abstract transform(input: SpeechInput, options?: SpeechToTextOptions): Promise<Text>;
}

// SpeechInput = Speech | Audio | Video | (Asset & SpeechRole)
// Model calls await castToSpeech(input) internally which calls await input.asSpeech()
```

### **Smart Usage Pattern**
```typescript
// STEP 1: Load media with smart auto-detection
const videoAsset = AssetLoader.load('file.mp4');  // Auto-detects format, applies role mixins
const audioAsset = AssetLoader.load('file.mp3');  // Auto-detects format, applies role mixins

// STEP 2: Transform with universal interface (FFmpeg happens automatically)
const result1 = await model.transform(videoAsset); // Model calls asSpeech() → FFmpeg extraction
const result2 = await model.transform(audioAsset); // Model calls asSpeech() → direct conversion

// STEP 3: Error handling via promise rejection
try {
  const textAsset = AssetLoader.load('document.txt');
  await model.transform(textAsset); // Throws: missing SpeechRole capability
} catch (error) {
  // Clear error message about unsupported input
}
```

## � TTS DUAL-SIGNATURE PATTERN

### **Problem: Voice Cloning as Options Breaks Clean Interface**
Previous approach mixed different operation modes through options:
```typescript
// INCONSISTENT - Basic TTS and voice cloning look the same
await model.transform(text, { voice: 'default' });
await model.transform(text, { voiceFile: 'voice.wav' }); // Different operation, same signature
```

### **Solution: Dual Transform Signatures - Same Pattern as Whisper STT**
Voice cloning is a fundamentally different operation that takes different inputs:

```typescript
// CONSISTENT WITH WHISPER PATTERN
// Whisper STT: Multiple input types, same method name
await whisperModel.transform(audio);     // Audio → Text
await whisperModel.transform(video);     // Video → Text (extracts audio)

// TTS: Multiple input combinations, same method name  
await ttsModel.transform(text);                    // Text → Speech (basic TTS)
await ttsModel.transform(text, voiceAudio);        // Text + Audio → Speech (voice cloning)
```

### **Key Benefits**
1. **Type Safety** - Compiler knows exactly what inputs are expected for each mode
2. **Consistency** - Same pattern as Whisper's multi-input handling  
3. **Separation of Concerns** - Voice cloning is clearly a different operation, not just an option
4. **Discoverability** - Developers can see all available transform signatures
5. **Clean Interface** - No magic options that change behavior dramatically

### **Implementation Pattern**
```typescript
abstract class TextToAudioModel extends Model {
  // Basic text-to-audio transformation (includes text-to-speech)
  abstract transform(input: Text | (Asset & TextRole)): Promise<Speech>;

  // Voice cloning transformation - accepts Speech (which Audio can play the role of)
  abstract transform(text: Text | (Asset & TextRole), voiceSpeech: Speech | (Asset & SpeechRole)): Promise<Speech>;
}

// Usage Examples with Smart Asset Loading
const text = AssetLoader.load('script.txt');       // Auto-detects text format
const voiceAudio = AssetLoader.load('voice.wav');  // Auto-detects audio, applies speech role

// Basic TTS
const basicSpeech = await ttsModel.transform(text);

// Voice cloning - voiceAudio automatically has asSpeech() due to smart loading
const clonedSpeech = await ttsModel.transform(text, voiceAudio); // Async role casting happens automatically

// Basic TTS
const basicSpeech = await model.transform(text);

// Voice cloning - clearly different signature
const clonedSpeech = await model.transform(text, voiceSpeech);
```

## �🏗️ SEPARATION OF CONCERNS ARCHITECTURE

### **Current Problem: Monolithic Services**
The existing `ChatterboxTTSDockerService` and `WhisperSTTService` mix multiple concerns:
- Docker management + API calls + business logic + MediaTransformer interface
- Not DRY, not extensible, not reusable

### **Solution: Clean Layer Separation**

#### **Layer 1: Service Management** (Infrastructure)
```typescript
// Generic Docker operations (KEEP - already DRY)
DockerComposeService

// Service-specific Docker configs (EXTRACT from monoliths)
ChatterboxDockerService  // Just Docker + health checks
WhisperDockerService     // Just Docker + health checks
```

#### **Layer 2: API Clients** (Pure Communication)
```typescript
// Pure HTTP API clients (EXTRACT from current monoliths)
ChatterboxAPIClient      // Just HTTP calls to Chatterbox API
WhisperAPIClient         // Just HTTP calls to Whisper API
OpenAIAPIClient          // HTTP calls to OpenAI API
ReplicateAPIClient       // HTTP calls to Replicate API
ElevenLabsAPIClient      // HTTP calls to ElevenLabs API
AnthropicAPIClient       // HTTP calls to Anthropic API
OllamaAPIClient          // HTTP calls to local Ollama API
OpenRouterAPIClient      // HTTP calls to OpenRouter API
TogetherAPIClient        // HTTP calls to Together AI API
```

#### **Layer 3: Smart Asset System with Dynamic Role Mixins** (Business Logic)
```typescript
// Smart Asset System with Dynamic Role Assignment
AssetLoader (smart factory)
├── Format Detection Registry
│   ├── Audio formats: MP3, WAV, FLAC → SpeechRole + AudioRole
│   ├── Video formats: MP4, AVI, MOV → VideoRole + SpeechRole + AudioRole (with FFmpeg)
│   ├── Text formats: TXT, MD, JSON → TextRole
│   └── Extensible for new formats
├── Dynamic Mixin Application
│   ├── withSpeechRole(Asset) → Asset & SpeechRole (with FFmpeg for video)
│   ├── withAudioRole(Asset) → Asset & AudioRole (with FFmpeg for video)
│   ├── withVideoRole(Asset) → Asset & VideoRole
│   └── withTextRole(Asset) → Asset & TextRole
└── Smart Loading Interface
    ├── AssetLoader.load(filePath) → Auto-detects format, applies appropriate mixins
    ├── AssetLoader.fromFile<T>(filePath) → Type-safe loading with role guarantees
    └── AssetLoader.fromBuffer(buffer, format) → Buffer-based loading with format specification

// Legacy Asset Types (still supported but superseded by smart loading)
├── MP3Asset extends withSpeechRole(withAudioRole(Asset))      // ⚠️ Legacy
├── MP4Asset extends withSpeechRole(withAudioRole(withVideoRole(Asset))) // ⚠️ Legacy
├── WAVAsset extends withSpeechRole(withAudioRole(Asset))      // ⚠️ Legacy
└── TextAsset extends withTextRole(Asset)                     // ⚠️ Legacy

// Model Hierarchy with Asset-Role Inputs/Outputs
Model (abstract base)
├── TextToAudioModel (abstract, includes text-to-speech functionality)
│   ├── ChatterboxTTSModel (concrete implementation)
│   ├── ChatterboxDockerModel (concrete implementation)
│   └── ReplicateTextToAudioModel (concrete implementation)
└── AudioToTextModel (abstract)
    └── SpeechToTextModel (abstract, extends AudioToTextModel)
        └── WhisperSTTModel (concrete implementation)

// Clean Model Interfaces with Autocasting
SpeechToTextModel.transform(input: Speech | (Asset & SpeechRole)): Promise<Text>
TextToSpeechModel.transform(input: Text | (Asset & TextRole)): Promise<Speech>

// Type-Safe Usage with Autocasting
const mp4Asset = MP4Asset.fromFile('video.mp4');  // Has SpeechRole
const transcript = await whisperModel.transform(mp4Asset);  // Autocasts to Speech
const newSpeech = await chatterboxModel.transform(transcript);  // Text → Speech
```

#### **Layer 4: Provider Role Interface Architecture** (Model Management + Infrastructure)

**Core Principle**: Providers implement capability interfaces with required service management methods and delegate to services for infrastructure.

```typescript
// Provider Role Interfaces - IMPLEMENTED ARCHITECTURE
export interface SpeechToTextProvider {
  /**
   * Create a speech-to-text model instance
   */
  createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel>;

  /**
   * Get supported speech-to-text models
   */
  getSupportedSpeechToTextModels(): string[];

  /**
   * Check if provider supports a specific STT model
   */
  supportsSpeechToTextModel(modelId: string): boolean;

  /**
   * Start the underlying service (no-op for remote providers, functional for Docker providers)
   */
  startService(): Promise<boolean>;

  /**
   * Stop the underlying service (no-op for remote providers, functional for Docker providers)
   */
  stopService(): Promise<boolean>;

  /**
   * Get service status (no-op for remote providers, functional for Docker providers)
   */
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

export interface TextToAudioProvider {
  /**
   * Create a text-to-audio model instance
   */
  createTextToAudioModel(modelId: string): Promise<TextToAudioModel>;

  /**
   * Get supported text-to-audio models
   */
  getSupportedTextToAudioModels(): string[];

  /**
   * Check if provider supports a specific TTS model
   */
  supportsTextToAudioModel(modelId: string): boolean;

  /**
   * Start the underlying service (no-op for remote providers, functional for Docker providers)
   */
  startService(): Promise<boolean>;

  /**
   * Stop the underlying service (no-op for remote providers, functional for Docker providers)
   */
  stopService(): Promise<boolean>;

  /**
   * Get service status (no-op for remote providers, functional for Docker providers)
   */
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

// ACTUAL IMPLEMENTATION - Direct Interface Implementation
export class WhisperDockerProvider extends LocalProvider implements SpeechToTextProvider {
  readonly id = 'whisper-docker';
  readonly name = 'Whisper Docker Provider';

  private dockerService?: WhisperDockerService;
  private apiClient?: WhisperAPIClient;

  // Core interface methods
  async createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel> {
    return this.createModel(modelId);
  }

  getSupportedSpeechToTextModels(): string[] {
    return this.getAvailableModels();
  }

  supportsSpeechToTextModel(modelId: string): boolean {
    return this.supportsModel(modelId);
  }

  // Service management methods (required, not optional)
  async startService(): Promise<boolean> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const started: boolean = await dockerService.startService();

    if (started) {
      const healthy: boolean = await dockerService.waitForHealthy(30000);
      return healthy;
    }

    return false;
  }

  async stopService(): Promise<boolean> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    return await dockerService.stopService();
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const status = await dockerService.getServiceStatus();

    return {
      running: status.running || false,
      healthy: status.health === 'healthy'
    };
  }

  // Implementation details
  async createModel(modelId: string): Promise<SpeechToTextModel> {
    const dockerService: WhisperDockerService = await this.getDockerService();
    const apiClient: WhisperAPIClient = await this.getAPIClient();

    const model: WhisperDockerModel = new WhisperDockerModel({
      dockerService,
      apiClient
    });

    return model;
  }
}

export class ChatterboxDockerProvider extends LocalProvider implements TextToAudioProvider {
  readonly id = 'chatterbox-docker';
  readonly name = 'Chatterbox Docker Provider';

  // Same pattern as WhisperDockerProvider
  // Implements all TextToAudioProvider methods including service management
}
```

**Key Architecture Points - IMPLEMENTED:**
1. **Required Service Management**: Service methods are required (not optional) in provider interfaces
2. **Direct Interface Implementation**: Providers directly implement role interfaces without mixins
3. **Type Safety**: Full TypeScript compliance with explicit type annotations
4. **Clean Delegation**: Providers delegate to services, services manage infrastructure
5. **Interface Compliance**: Docker providers implement service methods, remote providers can implement as no-ops
6. **Separation of Concerns**: Provider = Infrastructure + Model Creation, Model = Pure Transformation
7. **Testability**: Models can be unit tested independently of Docker infrastructure

#### **Layer 5: Direct Provider Usage with Type Safety** (Coordination)

**IMPLEMENTED PATTERN**: Direct provider instantiation with full service lifecycle management

```typescript
// ACTUAL IMPLEMENTATION - Integration Test Pattern
async function runIntegrationTest(): Promise<void> {
  // Create provider with explicit typing
  const provider: SpeechToTextProvider = new WhisperDockerProvider();

  // Service lifecycle management
  const serviceStarted: boolean = await provider.startService();
  assert(serviceStarted, 'Docker service started');

  const status: { running: boolean; healthy: boolean; error?: string } = await provider.getServiceStatus();
  assert(status.running, 'Docker service is running');
  assert(status.healthy, 'Docker service is healthy');

  // Model creation using interface method
  const model: SpeechToTextModel = await provider.createSpeechToTextModel('whisper-stt');
  const isAvailable: boolean = await model.isAvailable();
  assert(isAvailable, 'Model is available');

  // Smart Asset Loading - Auto-detects formats and applies role mixins
  const wavAudio = AssetLoader.load(wavPath);        // Auto-detects WAV, applies speech+audio roles
  const wavResult: Text = await model.transform(wavAudio);
  assert(!!wavResult.content, 'WAV result has content property');

  const mp3Audio = AssetLoader.load(mp3Path);        // Auto-detects MP3, applies speech+audio roles
  const mp3Result: Text = await model.transform(mp3Audio);
  assert(!!mp3Result.content, 'MP3 result has content property');

  const mp4Video = AssetLoader.load(mp4Path);        // Auto-detects MP4, applies video+speech+audio roles
  const mp4Result: Text = await model.transform(mp4Video); // FFmpeg extraction happens automatically
  assert(!!mp4Result.content, 'MP4 result has content property');

  // Service cleanup
  const stopped: boolean = await provider.stopService();
  assert(stopped, 'Service is stopped');
}

// TTS Implementation follows same pattern
async function runTTSTest(): Promise<void> {
  const provider: TextToSpeechProvider = new ChatterboxDockerProvider();

  await provider.startService();
  const model: TextToSpeechModel = await provider.createTextToSpeechModel('chatterbox-tts');

  // Basic TTS - smart asset loading
  const textFromString: Text = Text.fromString('Hello, this is a test');
  const stringResult: Speech = await model.transform(textFromString);

  const textFromFile = AssetLoader.load(testTextPath);     // Auto-detects text format, applies text role
  const fileResult: Speech = await model.transform(textFromFile);

  // Voice cloning TTS - smart asset loading with automatic async role casting
  const voiceAudio = AssetLoader.load('voice-sample.wav'); // Auto-detects WAV, applies speech+audio roles
  const clonedResult: Speech = await model.transform(textFromString, voiceAudio); // Async asSpeech() called automatically

  await provider.stopService();
}
```

## 🎯 FAL.AI INTEGRATION ARCHITECTURE (NEW)

### **Multi-Capability Provider System with Model-Specific Interfaces**

**Core Achievement**: Single fal.ai provider implementing multiple XProvider interfaces, with each model exposing its own specialized API while sharing common infrastructure.

### **XProvider → XtoYModel → Specialized Model Pattern**

**The Pattern**: fal.ai supports multiple transformation types through one provider, but each model needs its own specialized interface:

```typescript
// 1. ONE PROVIDER, MULTIPLE CAPABILITIES
export class FalAiProvider implements 
  TextToImageProvider,
  ImageToVideoProvider, 
  TextToVideoProvider,
  VideoToVideoProvider {
  
  constructor(private falAdapter: FalAiAdapter) {}
  
  // TextToImageProvider interface
  async createTextToImageModel(modelId: string): Promise<TextToImageModel> {
    switch(modelId) {
      case 'flux-pro':
        return new FalFluxProModel(this.falAdapter, modelId);
      case 'flux-dev':
        return new FalFluxDevModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported text-to-image model: ${modelId}`);
    }
  }
  
  // ImageToVideoProvider interface
  async createImageToVideoModel(modelId: string): Promise<ImageToVideoModel> {
    switch(modelId) {
      case 'framepack':
        return new FalFramePackModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported image-to-video model: ${modelId}`);
    }
  }
  
  // TextToVideoProvider interface
  async createTextToVideoModel(modelId: string): Promise<TextToVideoModel> {
    switch(modelId) {
      case 'runway-gen3':
        return new FalRunwayGen3Model(this.falAdapter, modelId);
      case 'stable-video-diffusion':
        return new FalStableVideoModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported text-to-video model: ${modelId}`);
    }
  }
  
  // VideoToVideoProvider interface
  async createVideoToVideoModel(modelId: string): Promise<VideoToVideoModel> {
    switch(modelId) {
      case 'face-swap':
        return new FalFaceSwapModel(this.falAdapter, modelId);
      case 'video-enhance':
        return new FalVideoEnhanceModel(this.falAdapter, modelId);
      default:
        throw new Error(`Unsupported video-to-video model: ${modelId}`);
    }
  }
}
```

### **Model-Specific Interfaces with Shared Infrastructure**

**Key Innovation**: Each fal.ai model gets its own specialized interface that matches its actual API, while sharing common infrastructure:

```typescript
// 2. SPECIALIZED MODEL IMPLEMENTATIONS
export class FalFramePackModel extends ImageToVideoModel {
  constructor(private falAdapter: FalAiAdapter, private modelId: string) {
    super({
      id: 'fal-framepack',
      name: 'FAL FramePack Animation',
      description: 'Animate static images with AI',
      version: '1.0.0',
      provider: 'fal.ai',
      capabilities: ['image-to-video', 'animation'],
      inputTypes: ['image'],
      outputTypes: ['video']
    });
  }
  
  // Standard transform method - unified interface
  async transform(image: ImageInput, options: {
    prompt: string;             // Required for FramePack
    num_frames?: number;        // 50-300, default 150
    fps?: number;              // 15-60, default 30
    guidance_scale?: number;   // 1-20, default 7.5
    video_length?: number;     // 2-10 seconds, default 5
    aspect_ratio?: '16:9' | '9:16' | '1:1';
    teacache?: boolean;        // Performance optimization
  }): Promise<Video> {
    const imageAsset = await castToImage(image);
    
    // Convert to fal.ai format
    const requestData = {
      prompt: options.prompt,
      image_url: await this.uploadAsset(imageAsset),
      num_frames: options.num_frames || 150,
      fps: options.fps || 30,
      guidance_scale: options.guidance_scale || 7.5,
      video_length: options.video_length || 5,
      aspect_ratio: options.aspect_ratio || '16:9',
      teacache: options.teacache !== false
    };
    
    // Call fal.ai API through adapter
    const result = await this.falAdapter.invoke('framepack', requestData);
    
    // Convert back to our Video asset
    return Video.fromUrl(result.video.url);
  }
}

export class FalFaceSwapModel extends VideoToVideoModel {
  // Standard transform method - unified VideoToVideoModel interface
  async transform(
    baseVideo: VideoInput, 
    overlayVideos: VideoInput | VideoInput[], 
    options: {
      face_restore?: boolean;
      background_enhance?: boolean;
      detection_threshold?: number;
    } = {}
  ): Promise<VideoCompositionResult> {
    // For face swap, overlayVideos should be the source image/video
    if (Array.isArray(overlayVideos)) {
      throw new Error('Face swap only supports single source image');
    }
    
    const sourceAsset = await castToImage(overlayVideos); // Source face
    const targetAsset = await castToVideo(baseVideo);     // Target video
    
    const requestData = {
      source_image: await this.uploadAsset(sourceAsset),
      target_video: await this.uploadAsset(targetAsset),
      face_restore: options.face_restore !== false,
      background_enhance: options.background_enhance !== false,
      detection_threshold: options.detection_threshold || 0.6
    };
    
    const result = await this.falAdapter.invoke('face-swap', requestData);
    const resultVideo = Video.fromUrl(result.video.url);
    
    return {
      composedVideo: resultVideo,
      metadata: {
        duration: result.duration || 0,
        resolution: result.resolution || '',
        aspectRatio: result.aspect_ratio || '',
        framerate: result.fps || 0,
        baseVideoInfo: { 
          duration: targetAsset.duration || 0, 
          resolution: targetAsset.resolution || '' 
        },
        overlayInfo: { 
          count: 1, 
          overlays: [{
            index: 0,
            startTime: 0,
            duration: result.duration || 0,
            position: 'face-swap',
            finalSize: { width: 0, height: 0 }
          }]
        }
      }
    };
  }
}

export class FalRunwayGen3Model extends TextToVideoModel {
  // Standard transform method - unified TextToVideoModel interface
  async transform(text: TextInput, options: {
    duration?: number;              // 2-10 seconds
    aspect_ratio?: '16:9' | '9:16' | '1:1';
    resolution?: '480p' | '720p' | '1080p';
    motion_strength?: number;       // 0.0-1.0
    camera_motion?: 'static' | 'pan' | 'zoom' | 'rotate';
    seed?: number;                  // For reproducibility
  } = {}): Promise<Video> {
    const textAsset = await castToText(text);
    
    const requestData = {
      prompt: textAsset.content,
      duration: options.duration || 5,
      aspect_ratio: options.aspect_ratio || '16:9',
      resolution: options.resolution || '720p',
      motion_strength: options.motion_strength || 0.5,
      camera_motion: options.camera_motion || 'static',
      seed: options.seed
    };
    
    const result = await this.falAdapter.invoke('runway-gen3', requestData);
    return Video.fromUrl(result.video.url);
  }
}
```

### **Usage Patterns - Type-Safe Model-Specific APIs**

**Clients use the unified transform() interface with model-specific options:**

```typescript
// 1. IMAGE ANIMATION with FramePack-specific options
const provider = ProviderFactory.getImageToVideoProvider('fal-ai');
const framePackModel = await provider.createImageToVideoModel('framepack');

const image = AssetLoader.load('photo.jpg');
const animatedVideo = await framePackModel.transform(image, {
  prompt: "dancing cat",
  num_frames: 150,
  fps: 30,
  aspect_ratio: '16:9',
  guidance_scale: 8.0,
  teacache: true
});

// 2. FACE SWAPPING with FaceSwap-specific options
const videoProvider = ProviderFactory.getVideoToVideoProvider('fal-ai');
const faceSwapModel = await videoProvider.createVideoToVideoModel('face-swap');

const sourceImage = AssetLoader.load('face.jpg');     // Source face (overlay)
const targetVideo = AssetLoader.load('video.mp4');    // Target video (base)
const result = await faceSwapModel.transform(targetVideo, sourceImage, {
  face_restore: true,
  background_enhance: true,
  detection_threshold: 0.7
});
const swappedVideo = result.composedVideo;

// 3. TEXT-TO-VIDEO with Runway-specific options
const textVideoProvider = ProviderFactory.getTextToVideoProvider('fal-ai');
const runwayModel = await textVideoProvider.createTextToVideoModel('runway-gen3');

const text = Text.fromString("a cat playing piano");
const generatedVideo = await runwayModel.transform(text, {
  duration: 8,
  aspect_ratio: '16:9',
  resolution: '1080p',
  motion_strength: 0.8,
  camera_motion: 'pan'
});

// 4. UNIVERSAL PATTERN - same transform() method across all models
const models = [framePackModel, faceSwapModel, runwayModel];
for (const model of models) {
  const result = await model.transform(input, modelSpecificOptions);
  // Each model interprets options according to its capabilities
}
```

## 🤖 TEXT-TO-TEXT LLM ARCHITECTURE (NEW)

### **TextToTextProvider Pattern for Large Language Models**

**Goal**: Integrate major LLM providers (Ollama, Anthropic, OpenAI, OpenRouter, Together) using the same provider/model/client architecture pattern as existing media providers.

### **Layer 1: TextToTextProvider Interface**

**Achievement**: Unified interface for all LLM providers with consistent capability declaration.

```typescript
// PRODUCTION INTERFACE - TextToText Provider Role
export interface TextToTextProvider {
  /**
   * Create a text-to-text model instance
   */
  createTextToTextModel(modelId: string): Promise<TextToTextModel>;

  /**
   * Get supported text-to-text models
   */
  getSupportedTextToTextModels(): string[];

  /**
   * Check if provider supports a specific LLM model
   */
  supportsTextToTextModel(modelId: string): boolean;

  /**
   * Start the underlying service (functional for Docker/local providers, no-op for remote APIs)
   */
  startService(): Promise<boolean>;

  /**
   * Stop the underlying service (functional for Docker/local providers, no-op for remote APIs)
   */
  stopService(): Promise<boolean>;

  /**
   * Get service status (functional for Docker/local providers, basic health check for remote APIs)
   */
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

// Add TEXT_GENERATION to MediaCapability enum
enum MediaCapability {
  TEXT_TO_SPEECH = 'text-to-speech',
  SPEECH_TO_TEXT = 'speech-to-text',
  TEXT_GENERATION = 'text-generation',    // NEW - LLM text generation
  TEXT_TO_TEXT = 'text-to-text',         // NEW - Alias for text generation
  IMAGE_GENERATION = 'image-generation',
  VIDEO_ANIMATION = 'video-animation',
  VIDEO_GENERATION = 'video-generation',
  // ... existing capabilities
}
```

### **Layer 2: Provider Implementations**

**Local Provider Pattern** (Ollama Docker/Local):
```typescript
// PRODUCTION IMPLEMENTATION - OllamaProvider
export class OllamaProvider extends LocalProvider implements TextToTextProvider {
  readonly id = 'ollama';
  readonly name = 'Ollama Local LLM Provider';
  readonly capabilities = [MediaCapability.TEXT_GENERATION];

  private dockerService?: OllamaDockerService;
  private apiClient?: OllamaAPIClient;

  // TextToTextProvider interface implementation
  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    const dockerService = await this.getDockerService();
    const apiClient = await this.getAPIClient();

    return new OllamaTextToTextModel({
      dockerService,
      apiClient,
      modelId
    });
  }

  getSupportedTextToTextModels(): string[] {
    return ['llama3.2', 'llama3.1', 'codellama', 'mistral', 'qwen2.5'];
  }

  supportsTextToTextModel(modelId: string): boolean {
    return this.getSupportedTextToTextModels().includes(modelId);
  }

  // Service management (functional for local Ollama)
  async startService(): Promise<boolean> {
    const dockerService = await this.getDockerService();
    const started = await dockerService.startService();
    
    if (started) {
      return await dockerService.waitForHealthy(60000);
    }
    return false;
  }

  async stopService(): Promise<boolean> {
    const dockerService = await this.getDockerService();
    return await dockerService.stopService();
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const dockerService = await this.getDockerService();
    const status = await dockerService.getServiceStatus();

    return {
      running: status.running || false,
      healthy: status.health === 'healthy',
      error: status.error
    };
  }

  // Infrastructure delegation
  protected async getDockerService(): Promise<OllamaDockerService> {
    if (!this.dockerService) {
      this.dockerService = new OllamaDockerService();
    }
    return this.dockerService;
  }

  protected async getAPIClient(): Promise<OllamaAPIClient> {
    if (!this.apiClient) {
      const baseUrl = await this.getBaseUrl();
      this.apiClient = new OllamaAPIClient(baseUrl);
    }
    return this.apiClient;
  }
}
```

**Remote Provider Pattern** (OpenAI, Anthropic, OpenRouter, Together):
```typescript
// PRODUCTION IMPLEMENTATION - OpenAIProvider
export class OpenAIProvider implements MediaProvider, TextToTextProvider {
  readonly id = 'openai';
  readonly name = 'OpenAI';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [
    MediaCapability.TEXT_GENERATION,
    MediaCapability.IMAGE_GENERATION, // DALL-E
    MediaCapability.SPEECH_TO_TEXT,   // Whisper API
    MediaCapability.TEXT_TO_SPEECH    // TTS API
  ];

  private config?: ProviderConfig;
  private apiClient?: OpenAIAPIClient;

  async configure(config: ProviderConfig): Promise<void> {
    this.config = config;
    
    if (!config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.apiClient = new OpenAIAPIClient({
      apiKey: config.apiKey,
      organization: config.organization
    });
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiClient) {
      return false;
    }

    try {
      return await this.apiClient.testConnection();
    } catch (error) {
      console.warn('OpenAI availability check failed:', error);
      return false;
    }
  }

  // TextToTextProvider interface implementation
  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    if (!this.apiClient) {
      throw new Error('Provider not configured');
    }

    return new OpenAITextToTextModel({
      apiClient: this.apiClient,
      modelId
    });
  }

  getSupportedTextToTextModels(): string[] {
    return ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo', 'o1-preview', 'o1-mini'];
  }

  supportsTextToTextModel(modelId: string): boolean {
    return this.getSupportedTextToTextModels().includes(modelId);
  }

  // Service management (no-ops for remote API providers)
  async startService(): Promise<boolean> {
    return true; // Remote APIs are always "started"
  }

  async stopService(): Promise<boolean> {
    return true; // No service to stop for remote APIs
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const isAvailable = await this.isAvailable();
    return {
      running: true, // Remote APIs are always "running"
      healthy: isAvailable,
      error: isAvailable ? undefined : 'API connection failed'
    };
  }

  // ... MediaProvider interface methods
  get models(): ProviderModel[] {
    return this.getSupportedTextToTextModels().map(modelId => ({
      id: modelId,
      name: modelId,
      description: `OpenAI ${modelId} model`,
      capabilities: [MediaCapability.TEXT_GENERATION],
      parameters: {},
      pricing: {
        inputCost: 0, // Would need real pricing data
        outputCost: 0,
        currency: 'USD'
      }
    }));
  }
}

// PRODUCTION IMPLEMENTATION - AnthropicProvider
export class AnthropicProvider implements MediaProvider, TextToTextProvider {
  readonly id = 'anthropic';
  readonly name = 'Anthropic';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [MediaCapability.TEXT_GENERATION];

  // Same pattern as OpenAI but with Anthropic-specific models
  getSupportedTextToTextModels(): string[] {
    return ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229'];
  }

  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    return new AnthropicTextToTextModel({
      apiClient: this.apiClient!,
      modelId
    });
  }

  // ... rest follows same pattern as OpenAI
}

// PRODUCTION IMPLEMENTATION - OpenRouterProvider
export class OpenRouterProvider implements MediaProvider, TextToTextProvider {
  readonly id = 'openrouter';
  readonly name = 'OpenRouter';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [MediaCapability.TEXT_GENERATION];

  // OpenRouter provides access to many models from different providers
  getSupportedTextToTextModels(): string[] {
    return [
      'anthropic/claude-3.5-sonnet',
      'openai/gpt-4o',
      'google/gemini-pro-1.5',
      'meta-llama/llama-3.2-90b-vision-instruct',
      'qwen/qwen-2.5-72b-instruct',
      'deepseek/deepseek-chat'
    ];
  }

  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    return new OpenRouterTextToTextModel({
      apiClient: this.apiClient!,
      modelId
    });
  }

  // ... rest follows same pattern
}

// PRODUCTION IMPLEMENTATION - TogetherProvider
export class TogetherProvider implements MediaProvider, TextToTextProvider {
  readonly id = 'together';
  readonly name = 'Together AI';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [MediaCapability.TEXT_GENERATION];

  getSupportedTextToTextModels(): string[] {
    return [
      'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo',
      'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo',
      'mistralai/Mixtral-8x7B-Instruct-v0.1',
      'Qwen/Qwen2.5-7B-Instruct-Turbo'
    ];
  }

  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    return new TogetherTextToTextModel({
      apiClient: this.apiClient!,
      modelId
    });
  }

  // ... rest follows same pattern
}
```

### **Layer 3: Service & Client Infrastructure**

**Docker Service Layer** (Ollama):
```typescript
// PRODUCTION PATTERN - Ollama Docker Service
export class OllamaDockerService extends DockerComposeService {
  protected getServiceName(): string { 
    return 'ollama'; 
  }
  
  protected getDockerComposePath(): string { 
    return './services/ollama'; 
  }
  
  async isServiceHealthy(): Promise<boolean> {
    return await this.checkContainerHealth('ollama');
  }

  async pullModel(modelName: string): Promise<boolean> {
    // Execute docker exec ollama ollama pull <model>
    try {
      await this.executeCommand(`docker exec ollama ollama pull ${modelName}`);
      return true;
    } catch (error) {
      console.error(`Failed to pull Ollama model ${modelName}:`, error);
      return false;
    }
  }

  async listModels(): Promise<string[]> {
    try {
      const result = await this.executeCommand('docker exec ollama ollama list');
      // Parse ollama list output to extract model names
      return this.parseModelList(result);
    } catch (error) {
      console.error('Failed to list Ollama models:', error);
      return [];
    }
  }
}
```

**API Client Layer** (Pure HTTP Communication):
```typescript
// PRODUCTION PATTERN - Ollama API Client
export class OllamaAPIClient {
  constructor(private baseUrl: string) {}

  async generateText(request: {
    model: string;
    prompt: string;
    stream?: boolean;
    options?: {
      temperature?: number;
      top_p?: number;
      max_tokens?: number;
    };
  }): Promise<OllamaResponse> {
    const response = await fetch(`${this.baseUrl}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: request.model,
        prompt: request.prompt,
        stream: request.stream || false,
        options: request.options
      })
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

// PRODUCTION PATTERN - OpenAI API Client
export class OpenAIAPIClient {
  constructor(private config: { apiKey: string; organization?: string }) {}

  async generateText(request: {
    model: string;
    messages: Array<{ role: string; content: string }>;
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
  }): Promise<OpenAIResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
        ...(this.config.organization && { 'OpenAI-Organization': this.config.organization })
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${this.config.apiKey}` }
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

// PRODUCTION PATTERN - Anthropic API Client
export class AnthropicAPIClient {
  constructor(private config: { apiKey: string }) {}

  async generateText(request: {
    model: string;
    messages: Array<{ role: string; content: string }>;
    max_tokens: number;
    temperature?: number;
  }): Promise<AnthropicResponse> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': this.config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async testConnection(): Promise<boolean> {
    try {
      // Anthropic doesn't have a simple health check endpoint
      // Try a minimal request to test authentication
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'test' }]
        })
      });
      return response.status !== 401; // Authentication error = bad API key
    } catch (error) {
      return false;
    }
  }
}
```

### **Layer 4: TextToTextModel Implementation**

**Model Pattern** (Pure Transformation Logic):
```typescript
// PRODUCTION PATTERN - TextToTextModel Base
export abstract class TextToTextModel extends Model {
  /**
   * Transform text input into text output using LLM
   */
  abstract transform(
    input: TextInput, 
    options?: TextToTextOptions
  ): Promise<Text>;

  /**
   * Multi-turn conversation support
   */
  abstract transformConversation(
    messages: ConversationMessage[],
    options?: TextToTextOptions
  ): Promise<Text>;

  /**
   * Streaming response support
   */
  abstract transformStream(
    input: TextInput,
    options?: TextToTextOptions
  ): AsyncGenerator<Text, Text, unknown>;
}

// TextInput = Text | string | (Asset & TextRole)
// TextToTextOptions = { temperature?, max_tokens?, system_prompt?, etc. }

// PRODUCTION IMPLEMENTATION - OllamaTextToTextModel
export class OllamaTextToTextModel extends TextToTextModel {
  constructor(private context: {
    dockerService: OllamaDockerService;
    apiClient: OllamaAPIClient;
    modelId: string;
  }) {
    super({
      id: `ollama-${context.modelId}`,
      name: `Ollama ${context.modelId}`,
      description: `Local Ollama model: ${context.modelId}`,
      version: '1.0.0',
      provider: 'ollama',
      capabilities: ['text-generation'],
      inputTypes: ['text'],
      outputTypes: ['text']
    });
  }

  async transform(
    input: TextInput,
    options: TextToTextOptions = {}
  ): Promise<Text> {
    // 1. Ensure Ollama service is running
    await this.ensureServiceRunning();

    // 2. Cast input to Text
    const text = await castToText(input);

    // 3. Call Ollama API
    const response = await this.context.apiClient.generateText({
      model: this.context.modelId,
      prompt: text.content,
      options: {
        temperature: options.temperature,
        top_p: options.top_p,
        max_tokens: options.max_tokens
      }
    });

    // 4. Return typed result
    return new Text(response.response, 'en', 1.0, {
      model: this.context.modelId,
      provider: 'ollama',
      usage: response.eval_count ? {
        prompt_tokens: response.prompt_eval_count,
        completion_tokens: response.eval_count,
        total_tokens: (response.prompt_eval_count || 0) + (response.eval_count || 0)
      } : undefined
    });
  }

  async transformConversation(
    messages: ConversationMessage[],
    options: TextToTextOptions = {}
  ): Promise<Text> {
    // Convert conversation to single prompt for Ollama
    const prompt = this.formatConversationAsPrompt(messages);
    return await this.transform(Text.fromString(prompt), options);
  }

  async *transformStream(
    input: TextInput,
    options: TextToTextOptions = {}
  ): AsyncGenerator<Text, Text, unknown> {
    await this.ensureServiceRunning();
    const text = await castToText(input);

    // Ollama streaming implementation
    const response = await fetch(`${this.context.apiClient.baseUrl}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: this.context.modelId,
        prompt: text.content,
        stream: true,
        options: {
          temperature: options.temperature,
          max_tokens: options.max_tokens
        }
      })
    });

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    let fullResponse = '';
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            if (data.response) {
              fullResponse += data.response;
              yield new Text(data.response, 'en', 1.0, { partial: true });
            }
          } catch (e) {
            // Skip invalid JSON lines
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return new Text(fullResponse, 'en', 1.0, {
      model: this.context.modelId,
      provider: 'ollama'
    });
  }

  private async ensureServiceRunning(): Promise<void> {
    const status = await this.context.dockerService.getServiceStatus();
    if (!status.running || status.health !== 'healthy') {
      throw new Error('Ollama service is not running or healthy');
    }
  }

  private formatConversationAsPrompt(messages: ConversationMessage[]): string {
    return messages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n') + '\nassistant:';
  }
}

// PRODUCTION IMPLEMENTATION - OpenAITextToTextModel
export class OpenAITextToTextModel extends TextToTextModel {
  constructor(private context: {
    apiClient: OpenAIAPIClient;
    modelId: string;
  }) {
    super({
      id: `openai-${context.modelId}`,
      name: `OpenAI ${context.modelId}`,
      description: `OpenAI model: ${context.modelId}`,
      version: '1.0.0',
      provider: 'openai',
      capabilities: ['text-generation'],
      inputTypes: ['text'],
      outputTypes: ['text']
    });
  }

  async transform(
    input: TextInput,
    options: TextToTextOptions = {}
  ): Promise<Text> {
    const text = await castToText(input);

    const messages = options.system_prompt 
      ? [
          { role: 'system', content: options.system_prompt },
          { role: 'user', content: text.content }
        ]
      : [{ role: 'user', content: text.content }];

    const response = await this.context.apiClient.generateText({
      model: this.context.modelId,
      messages,
      temperature: options.temperature,
      max_tokens: options.max_tokens
    });

    return new Text(
      response.choices[0].message.content,
      'en',
      1.0,
      {
        model: this.context.modelId,
        provider: 'openai',
        usage: response.usage
      }
    );
  }

  async transformConversation(
    messages: ConversationMessage[],
    options: TextToTextOptions = {}
  ): Promise<Text> {
    const openaiMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    if (options.system_prompt) {
      openaiMessages.unshift({ role: 'system', content: options.system_prompt });
    }

    const response = await this.context.apiClient.generateText({
      model: this.context.modelId,
      messages: openaiMessages,
      temperature: options.temperature,
      max_tokens: options.max_tokens
    });

    return new Text(
      response.choices[0].message.content,
      'en',
      1.0,
      {
        model: this.context.modelId,
        provider: 'openai',
        usage: response.usage
      }
    );
  }

  async *transformStream(
    input: TextInput,
    options: TextToTextOptions = {}
  ): AsyncGenerator<Text, Text, unknown> {
    const text = await castToText(input);

    const messages = options.system_prompt 
      ? [
          { role: 'system', content: options.system_prompt },
          { role: 'user', content: text.content }
        ]
      : [{ role: 'user', content: text.content }];

    // OpenAI streaming implementation
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.context.apiClient.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.context.modelId,
        messages,
        temperature: options.temperature,
        max_tokens: options.max_tokens,
        stream: true
      })
    });

    const reader = response.body?.getReader();
    if (!reader) throw new Error('No response body');

    let fullResponse = '';
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n').filter(line => line.startsWith('data: '));

        for (const line of lines) {
          const data = line.slice(6); // Remove 'data: ' prefix
          if (data === '[DONE]') continue;

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            if (content) {
              fullResponse += content;
              yield new Text(content, 'en', 1.0, { partial: true });
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return new Text(fullResponse, 'en', 1.0, {
      model: this.context.modelId,
      provider: 'openai'
    });
  }
}
```

### **Layer 5: Usage Patterns**

**Direct Provider Usage with Type Safety**:
```typescript
// LOCAL LLM USAGE - Ollama
async function runOllamaTest(): Promise<void> {
  const provider: TextToTextProvider = new OllamaProvider();

  // Service lifecycle management (functional for local Ollama)
  const serviceStarted = await provider.startService();
  assert(serviceStarted, 'Ollama service started');

  const status = await provider.getServiceStatus();
  assert(status.running && status.healthy, 'Ollama service is healthy');

  // Model creation
  const model = await provider.createTextToTextModel('llama3.2');
  
  // Single-turn text transformation
  const input = Text.fromString('Explain quantum computing in simple terms.');
  const response = await model.transform(input, {
    temperature: 0.7,
    max_tokens: 500
  });
  
  console.log('Ollama Response:', response.content);

  // Multi-turn conversation
  const conversation: ConversationMessage[] = [
    { role: 'user', content: 'What is machine learning?' },
    { role: 'assistant', content: 'Machine learning is...' },
    { role: 'user', content: 'Give me a practical example.' }
  ];
  
  const conversationResponse = await model.transformConversation(conversation);
  console.log('Conversation Response:', conversationResponse.content);

  // Streaming response
  console.log('Streaming Response:');
  for await (const chunk of model.transformStream(input)) {
    process.stdout.write(chunk.content);
  }

  await provider.stopService();
}

// REMOTE LLM USAGE - OpenAI
async function runOpenAITest(): Promise<void> {
  const provider: TextToTextProvider = new OpenAIProvider();
  
  await provider.configure({ apiKey: process.env.OPENAI_API_KEY! });
  
  // Service status (basic health check for remote APIs)
  const status = await provider.getServiceStatus();
  assert(status.healthy, 'OpenAI API is available');

  // Model creation
  const model = await provider.createTextToTextModel('gpt-4o');
  
  // Text transformation with system prompt
  const input = Text.fromString('Write a haiku about programming.');
  const response = await model.transform(input, {
    system_prompt: 'You are a creative poet who specializes in technical haikus.',
    temperature: 0.8,
    max_tokens: 100
  });
  
  console.log('OpenAI Response:', response.content);
  console.log('Usage:', response.metadata.usage);

  // Multi-turn conversation (native OpenAI format)
  const conversation: ConversationMessage[] = [
    { role: 'user', content: 'I need help with Python.' },
    { role: 'assistant', content: 'I\'d be happy to help! What specific Python topic?' },
    { role: 'user', content: 'How do I handle exceptions?' }
  ];
  
  const conversationResponse = await model.transformConversation(conversation, {
    temperature: 0.3
  });
  console.log('Conversation Response:', conversationResponse.content);
}

// MULTI-PROVIDER USAGE - Provider Factory Pattern
async function runMultiProviderComparison(): Promise<void> {
  const providers: TextToTextProvider[] = [
    new OllamaProvider(),
    new OpenAIProvider(), 
    new AnthropicProvider(),
    new OpenRouterProvider()
  ];

  const prompt = Text.fromString('Explain the concept of recursion with a simple example.');

  for (const provider of providers) {
    console.log(`\n=== Testing ${provider.name} ===`);
    
    try {
      // Configure if needed
      if (provider.id === 'openai') {
        await provider.configure({ apiKey: process.env.OPENAI_API_KEY! });
      } else if (provider.id === 'anthropic') {
        await provider.configure({ apiKey: process.env.ANTHROPIC_API_KEY! });
      } else if (provider.id === 'openrouter') {
        await provider.configure({ apiKey: process.env.OPENROUTER_API_KEY! });
      }

      // Start service if local
      if (provider.id === 'ollama') {
        await provider.startService();
      }

      // Get available models
      const models = provider.getSupportedTextToTextModels();
      console.log('Available models:', models);

      // Create model instance
      const model = await provider.createTextToTextModel(models[0]);
      
      // Transform text
      const result = await model.transform(prompt, { temperature: 0.7 });
      console.log('Response:', result.content.substring(0, 200) + '...');

    } catch (error) {
      console.error(`${provider.name} failed:`, error.message);
    } finally {
      // Cleanup
      if (provider.id === 'ollama') {
        await provider.stopService();
      }
    }
  }
}
```

### **Key Architecture Benefits**

1. **✅ Consistent Interface** - Same TextToTextProvider interface for all LLM providers
2. **✅ Local + Remote Support** - Ollama (Docker) and cloud APIs unified under same interface
3. **✅ Service Management** - Full lifecycle management for local providers, health checks for remote
4. **✅ Type Safety** - Full TypeScript support with proper input/output typing
5. **✅ Smart Asset Integration** - Works with AssetLoader.load() for text files
6. **✅ Streaming Support** - Native async generator support for real-time responses
7. **✅ Conversation Support** - Multi-turn conversation handling for all providers
8. **✅ Provider Discovery** - Factory pattern enables capability-based provider selection
9. **✅ Extensibility** - Easy to add new LLM providers following the same pattern
10. **✅ Error Handling** - Consistent error handling and fallback mechanisms

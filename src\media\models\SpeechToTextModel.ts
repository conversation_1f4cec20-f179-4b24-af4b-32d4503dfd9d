/**
 * SpeechToTextModel - Abstract Base Class
 */

import { ModelMetadata } from './Model';
import { Text } from '../assets/roles';
import { AudioInput } from '../assets/casting';

export interface AudioToTextOptions {
  language?: string;
  task?: 'transcribe' | 'translate';
  wordTimestamps?: boolean;
}

/**
 * Abstract base class for audio-to-text models (including speech-to-text)
 */
export abstract class SpeechToTextModel {
  protected metadata: ModelMetadata;

  constructor(metadata: ModelMetadata) {
    this.metadata = metadata;
  }

  /**
   * Transform audio to text
   */
  abstract transform(input: AudioInput, options?: AudioToTextOptions): Promise<Text>;

  /**
   * Check if the model is available
   */
  abstract isAvailable(): Promise<boolean>;
}

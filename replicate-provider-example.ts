/**
 * Example: How to Use ReplicateProvider
 * 
 * This example shows how to configure and use the ReplicateProvider
 * to generate content with different Replicate models.
 */

import { ReplicateProvider } from './packages/providers/src/remote/ReplicateProvider';
import { Text } from './src/media/assets/roles';

async function demonstrateReplicateProvider() {
  console.log('🚀 ReplicateProvider Usage Example\n');

  // 1. Create and configure the provider
  console.log('1️⃣ Creating and configuring ReplicateProvider...');
  const provider = new ReplicateProvider();
  
  await provider.configure({
    apiKey: 'r8_your_replicate_api_key_here' // Replace with your actual API key
  });

  // 2. Check if provider is available
  console.log('2️⃣ Checking provider availability...');
  const isAvailable = await provider.isAvailable();
  console.log(`   Provider available: ${isAvailable}\n`);

  if (!isAvailable) {
    console.log('❌ Provider not available. Check your API key and internet connection.');
    return;
  }

  // 3. Text-to-Audio Example (TTS)
  console.log('3️⃣ Text-to-Audio Example:');
  try {
    // Get a TTS model (you'd need to find actual Replicate TTS models)
    const ttsModel = await provider.createTextToAudioModel('some-replicate-tts-model');
      // Generate speech
    const speech = await ttsModel.transform(Text.fromString("Hello, this is a test of Replicate TTS!"), {
      language: 'en',
      speed: 1.0
    });

    console.log('   ✅ Audio generated:', speech.toString());
    console.log('   📁 Result URL:', (speech as any).metadata?.resultUrl);
  } catch (error) {
    console.log('   ⚠️ TTS Example failed:', error.message);
  }
  console.log('');
  // 4. Text-to-Video Example  
  console.log('4️⃣ Text-to-Video Example:');
  try {
    // Get a text-to-video model (like Luma Dream Machine)
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
      // Generate video (it automatically downloads the file!)
    const video = await videoModel.transform(Text.fromString("A cat playing piano in a cozy living room"), {
      duration: 5,
      aspectRatio: '16:9',
      fps: 24,
      motionStrength: 0.8
    });
    
    console.log('   ✅ Video generated and downloaded:', video.toString());
    console.log('   🎬 Video URL:', (video as any).metadata?.url);
    console.log('   📁 Local file:', (video as any).metadata?.localPath);
    console.log('   ⏱️  Duration:', video.getDuration(), 'seconds');
    console.log('   📐 Dimensions:', video.getDimensions());
    
    // Optional: Copy to custom location
    if ((video as any).metadata?.localPath) {
      const customPath = './my-videos/cat-piano.mp4';
      const fs = require('fs');
      const path = require('path');
      
      // Ensure directory exists
      fs.mkdirSync(path.dirname(customPath), { recursive: true });
      
      // Copy file
      fs.copyFileSync((video as any).metadata.localPath, customPath);
      console.log('   📋 Copied to:', customPath);
    }
  } catch (error) {
    console.log('   ⚠️ Text-to-Video Example failed:', error.message);
  }
  console.log('');

  // 5. Model Discovery Example
  console.log('5️⃣ Model Discovery Example:');
  try {
    // Discover a specific model
    const modelMetadata = await (provider as any).getModelMetadata('black-forest-labs/flux-1.1-pro');
    console.log('   📋 Model discovered:', modelMetadata.name);
    console.log('   📝 Description:', modelMetadata.description);
    console.log('   🏷️  Category:', modelMetadata.category);
    console.log('   ⚙️  Parameters:', Object.keys(modelMetadata.parameters || {}).slice(0, 5));
  } catch (error) {
    console.log('   ⚠️ Model discovery failed:', error.message);
  }
  console.log('');

  // 6. Provider Capabilities
  console.log('6️⃣ Provider Capabilities:');
  console.log('   🎯 Supported capabilities:', provider.capabilities);
  console.log('   🔧 Provider type:', provider.type);
  console.log('   🆔 Provider ID:', provider.id);
  console.log('');

  // 7. Health Check
  console.log('7️⃣ Health Check:');
  const health = await provider.getHealth();
  console.log('   💚 Status:', health.status);
  console.log('   ⏰ Uptime:', health.uptime.toFixed(2), 'seconds');
  console.log('');

  console.log('✨ ReplicateProvider demo complete!');
}

// Advanced usage examples
async function advancedUsageExamples() {
  console.log('\n🔥 Advanced Usage Examples\n');

  const provider = new ReplicateProvider();
  await provider.configure({
    apiKey: 'r8_your_replicate_api_key_here'
  });

  // Example 1: Using specific model parameters
  console.log('🎨 Advanced Text-to-Video with custom parameters:');
  try {
    const videoModel = await provider.createTextToVideoModel('runwayml/gen-3-alpha-turbo');
    
    const video = await videoModel.transform(Text.fromString("A majestic dragon flying over a medieval castle"), {
      duration: 10,
      aspectRatio: '21:9', // Cinematic aspect ratio
      fps: 30,
      seed: 42, // For reproducible results
      guidanceScale: 7.5,
      negativePrompt: "blurry, low quality, distorted"
    });
    
    console.log('   ✅ Cinematic video created!');
  } catch (error) {
    console.log('   ⚠️ Advanced video generation failed:', error.message);
  }

  // Example 2: Voice cloning (when supported)
  console.log('\n🎤 Voice Cloning Example (when TTS model supports it):');
  try {
    const ttsModel = await provider.createTextToAudioModel('voice-cloning-model');
    
    // This would work if the model supports voice cloning
    // const voiceReference = // Load reference audio
    // const clonedSpeech = await ttsModel.transform(
    //   "This is cloned speech", 
    //   voiceReference, 
    //   { language: 'en' }
    // );
    
    console.log('   ℹ️ Voice cloning requires reference audio and supporting model');
  } catch (error) {
    console.log('   ⚠️ Voice cloning example failed:', error.message);
  }

  // Example 3: Batch processing
  console.log('\n🔄 Batch Processing Example:');
  const prompts = [
    "A sunset over the ocean",
    "A bustling city street at night", 
    "A peaceful forest meadow"
  ];

  try {
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
    
    console.log('   🎬 Generating multiple videos...');
    const videoPromises = prompts.map(async (prompt, index) => {
      try {
        const video = await videoModel.transform(Text.fromString(prompt), {
          duration: 3,
          aspectRatio: '1:1', // Square format
          seed: index // Different seed for each
        });
        return { prompt, video, success: true };
      } catch (error) {
        return { prompt, error: error.message, success: false };
      }
    });

    const results = await Promise.all(videoPromises);
    results.forEach((result, index) => {
      if (result.success) {
        console.log(`   ✅ Video ${index + 1}: ${result.prompt} - Generated!`);
      } else {
        console.log(`   ❌ Video ${index + 1}: ${result.prompt} - Failed: ${result.error}`);
      }
    });
  } catch (error) {
    console.log('   ⚠️ Batch processing failed:', error.message);
  }
}

// Error handling example
async function errorHandlingExample() {
  console.log('\n🛡️ Error Handling Examples\n');

  const provider = new ReplicateProvider();

  // Example 1: Invalid API key
  console.log('1️⃣ Testing invalid API key:');
  try {
    await provider.configure({ apiKey: 'invalid_key' });
    const isAvailable = await provider.isAvailable();
    console.log('   Result:', isAvailable ? 'Available' : 'Not Available');
  } catch (error) {
    console.log('   ❌ Expected error:', error.message);
  }

  // Example 2: Non-existent model
  console.log('\n2️⃣ Testing non-existent model:');
  await provider.configure({ apiKey: 'r8_valid_key_here' });
  try {
    await provider.createTextToVideoModel('non-existent/model');
  } catch (error) {
    console.log('   ❌ Expected error:', error.message);
  }

  // Example 3: Invalid input
  console.log('\n3️⃣ Testing invalid input:');
  try {
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
    await videoModel.transform(Text.fromString(''), { duration: -1 }); // Invalid duration
  } catch (error) {
    console.log('   ❌ Expected error:', error.message);
  }
}

// Production usage example
function productionUsageExample() {
  console.log('\n🏭 Production Usage Guidelines\n');

  console.log(`
🔧 **Configuration:**
- Store API key in environment variables (REPLICATE_API_KEY)
- Use proper error handling and retries
- Implement rate limiting for API calls
- Cache model metadata to reduce discovery calls

📊 **Monitoring:**
- Track generation success/failure rates
- Monitor API usage and costs
- Log generation parameters for debugging
- Set up alerts for API failures

🔒 **Security:**
- Never expose API keys in client-side code
- Validate all user inputs before generation
- Implement usage quotas per user
- Use HTTPS for all API communications

⚡ **Performance:**
- Batch similar requests when possible
- Use appropriate timeouts for long-running generations
- Implement progress tracking for user feedback
- Cache frequently used models

💰 **Cost Optimization:**
- Monitor per-model costs
- Set reasonable limits on generation parameters
- Implement usage tracking per user
- Use lower-cost models for testing/development
  `);
}

// Run the examples
if (require.main === module) {
  (async () => {
    try {
      await demonstrateReplicateProvider();
      await advancedUsageExamples();
      await errorHandlingExample();
      productionUsageExample();
    } catch (error) {
      console.error('Demo failed:', error);
    }
  })();
}

export {
  demonstrateReplicateProvider,
  advancedUsageExamples,
  errorHandlingExample,
  productionUsageExample
};

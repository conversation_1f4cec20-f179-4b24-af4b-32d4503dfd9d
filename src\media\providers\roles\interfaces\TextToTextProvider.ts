/**
 * TextGenerationProvider Interface
 * 
 * Provider role for text generation capabilities.
 */

import { TextToTextModel } from '../../../models/TextToTextModel';

/**
 * Text Generation Provider Role
 */
export interface TextoTextProvider {
  createTextToTextModel(modelId: string): Promise<TextToTextModel>; // TODO: Define TextGenerationModel
  getSupportedTextToTextModels(): string[];
  supportsTextToTextModel(modelId: string): boolean;
}

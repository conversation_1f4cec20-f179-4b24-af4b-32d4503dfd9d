/**
 * Together AI Text-to-Image Model
 * 
 * Concrete implementation of TextToImageModel for Together.ai's image generation API.
 * Supports FLUX and other image generation models.
 */

import { TextToImageModel, TextToImageOptions, Image } from './TextToImageModel';
import { ModelMetadata } from './Model';
import { TextInput, castToText } from '../assets/casting';
import { TogetherAPIClient } from '../clients/TogetherAPIClient';
import axios from 'axios';

export interface TogetherTextToImageOptions extends TextToImageOptions {
  model?: string;
  prompt?: string;
  negative_prompt?: string;
  width?: number;
  height?: number;
  steps?: number;
  n?: number;
  response_format?: 'url' | 'b64_json';
  seed?: number;
}

export interface TogetherTextToImageConfig {
  apiClient: TogetherAPIClient;
  modelId: string;
  metadata?: Partial<ModelMetadata>;
}

export class TogetherTextToImageModel extends TextToImageModel {
  private apiClient: TogetherAPIClient;
  private modelId: string;

  constructor(config: TogetherTextToImageConfig) {
    const metadata: ModelMetadata = {
      id: config.modelId,
      name: config.metadata?.name || `Together ${config.modelId}`,
      description: config.metadata?.description || `Together AI text-to-image model: ${config.modelId}`,
      version: config.metadata?.version || '1.0.0',
      provider: 'together',
      capabilities: ['image-generation'],
      inputTypes: ['text'],
      outputTypes: ['image'],
      ...config.metadata
    };

    super(metadata);
    this.apiClient = config.apiClient;
    this.modelId = config.modelId;
  }

  /**
   * Transform text to image using Together AI
   */
  async transform(input: TextInput, options?: TogetherTextToImageOptions): Promise<Image> {
    const startTime = Date.now();

    // Cast input to Text
    const text = await castToText(input);

    // Validate text data
    if (!text.isValid()) {
      throw new Error('Invalid text data provided');
    }

    try {
      // Prepare request payload for Together AI image generation
      const requestPayload = {
        model: this.modelId,
        prompt: text.content,
        negative_prompt: options?.negativePrompt || options?.negative_prompt,
        width: options?.width || 1024,
        height: options?.height || 1024,
        steps: this.getValidStepsForModel(options?.steps || 4),
        n: options?.n || 1,
        response_format: options?.response_format || 'url',
        seed: options?.seed
      };

      // Remove undefined values
      Object.keys(requestPayload).forEach(key => {
        if (requestPayload[key] === undefined) {
          delete requestPayload[key];
        }
      });

      // Make request to Together AI image generation endpoint
      const response = await this.makeImageRequest(requestPayload);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Handle response based on format
      let imageData: Buffer;
      let imageUrl: string | undefined;

      if (response.data && response.data.length > 0) {
        const imageResult = response.data[0];
        
        if (imageResult.url) {
          // Download image from URL
          imageUrl = imageResult.url;
          const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
          imageData = Buffer.from(imageResponse.data);
        } else if (imageResult.b64_json) {
          // Decode base64 image
          imageData = Buffer.from(imageResult.b64_json, 'base64');
        } else {
          throw new Error('No image data received from Together AI');
        }
      } else {
        throw new Error('No image data in response from Together AI');
      }

      // Create Image result
      const result = new Image(
        imageData,
        options?.format || 'png',
        {
          processingTime,
          model: this.modelId,
          provider: 'together',
          prompt: text.content,
          negativePrompt: options?.negativePrompt,
          width: options?.width || 1024,
          height: options?.height || 1024,
          steps: options?.steps || 20,
          seed: options?.seed,
          url: imageUrl
        },
        text.sourceAsset // Preserve source Asset reference
      );

      return result;

    } catch (error) {
      throw new Error(`Together AI image generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Make image generation request to Together AI
   */
  private async makeImageRequest(payload: any): Promise<any> {
    try {
      // Use the API client's underlying axios instance
      const response = await (this.apiClient as any).client.post('/images/generations', payload);
      return response.data;
    } catch (error) {
      if (error.response) {
        const errorMessage = error.response.data?.error?.message || error.response.statusText;
        throw new Error(`Together AI API error (${error.response.status}): ${errorMessage}`);
      }
      throw error;
    }
  }

  /**
   * Check if the model is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await this.apiClient.testConnection();
    } catch (error) {
      console.warn(`Together AI image model ${this.modelId} availability check failed:`, error);
      return false;
    }
  }

  /**
   * Get model-specific information
   */
  getModelInfo(): { id: string; provider: string; capabilities: string[] } {
    return {
      id: this.modelId,
      provider: 'together',
      capabilities: ['image-generation', 'text-to-image']
    };
  }

  /**
   * Get supported parameters for this model
   */
  getSupportedParameters(): string[] {
    return [
      'width',
      'height',
      'steps',
      'seed',
      'negativePrompt',
      'negative_prompt',
      'n',
      'response_format'
    ];
  }

  /**
   * Get model display name
   */
  getDisplayName(): string {
    // Convert model ID to readable name
    const parts = this.modelId.split('/');
    if (parts.length >= 2) {
      const [org, model] = parts;
      return `${org.charAt(0).toUpperCase() + org.slice(1)} ${model.replace(/-/g, ' ')}`;
    }
    return this.modelId;
  }

  /**
   * Check if this is a free model
   */
  async isFreeModel(): Promise<boolean> {
    try {
      const modelInfo = await this.apiClient.getModelInfo(this.modelId);
      if (modelInfo?.pricing) {
        return modelInfo.pricing.input === 0 && modelInfo.pricing.output === 0;
      }
      // If no pricing info, check if it's a known free model
      const freeModels = [
        'black-forest-labs/FLUX.1-schnell-Free',
        'black-forest-labs/FLUX.1-schnell',
        'black-forest-labs/FLUX.1-dev'
      ];
      return freeModels.includes(this.modelId);
    } catch (error) {
      console.warn(`Could not determine if model ${this.modelId} is free:`, error);
      return false;
    }
  }

  /**
   * Get valid steps count for this model
   */
  private getValidStepsForModel(requestedSteps: number): number {
    // FLUX.1-schnell models have strict step limits
    if (this.modelId.includes('FLUX.1-schnell')) {
      return Math.min(Math.max(requestedSteps, 1), 4); // 1-4 steps only
    }

    // FLUX.1-dev models typically allow more steps
    if (this.modelId.includes('FLUX.1-dev')) {
      return Math.min(Math.max(requestedSteps, 1), 50); // 1-50 steps
    }

    // FLUX.1-pro models
    if (this.modelId.includes('FLUX.1-pro')) {
      return Math.min(Math.max(requestedSteps, 1), 50); // 1-50 steps
    }

    // Default for other models
    return Math.min(Math.max(requestedSteps, 1), 20);
  }

  /**
   * Get recommended image dimensions for this model
   */
  getRecommendedDimensions(): { width: number; height: number }[] {
    // FLUX models typically work well with these dimensions
    if (this.modelId.includes('FLUX')) {
      return [
        { width: 1024, height: 1024 }, // Square
        { width: 1152, height: 896 },  // Landscape
        { width: 896, height: 1152 },  // Portrait
        { width: 1344, height: 768 },  // Wide landscape
        { width: 768, height: 1344 }   // Tall portrait
      ];
    }

    // Default dimensions
    return [
      { width: 512, height: 512 },
      { width: 768, height: 768 },
      { width: 1024, height: 1024 }
    ];
  }

  /**
   * Get model-specific parameter constraints
   */
  getParameterConstraints(): Record<string, any> {
    if (this.modelId.includes('FLUX.1-schnell')) {
      return {
        steps: { min: 1, max: 4, default: 4 },
        width: { min: 256, max: 1440, default: 1024 },
        height: { min: 256, max: 1440, default: 1024 }
      };
    }

    if (this.modelId.includes('FLUX.1-dev') || this.modelId.includes('FLUX.1-pro')) {
      return {
        steps: { min: 1, max: 50, default: 20 },
        width: { min: 256, max: 1440, default: 1024 },
        height: { min: 256, max: 1440, default: 1024 }
      };
    }

    // Default constraints
    return {
      steps: { min: 1, max: 20, default: 10 },
      width: { min: 256, max: 2048, default: 1024 },
      height: { min: 256, max: 2048, default: 1024 }
    };
  }
}

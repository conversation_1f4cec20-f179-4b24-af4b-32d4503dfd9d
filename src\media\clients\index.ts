/**
 * Media Clients Index
 * 
 * Exports all media client functionality including FFMPEG clients
 */

// FFMPEG API Client (original service-based client)
export {
  FFMPEGAPIClient,
  type FFMPEGClientConfig,
  type AudioExtractionOptions,
  type AudioConversionOptions,
  type AudioExtractionResult,
  type <PERSON>Health,
  type <PERSON>CompositionOptions,
  type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ult,
  type ApiR<PERSON>ponse
} from './FFMPEGAPIClient';

// FFMPEG Local Client (new local implementation)
export {
  FFMPEGLocalClient,
  type FFMPEGLocalConfig
} from './FFMPEGLocalClient';

// FFMPEG Client Factory and utilities
export {
  FFMPEGClientFactory,
  FFMPEGClientWrapper,
  type <PERSON><PERSON><PERSON><PERSON>Client,
  type FF<PERSON><PERSON><PERSON>lientType,
  type FFMPEGFactoryConfig,
  createFF<PERSON><PERSON>Client,
  createFF<PERSON>EGAPIClient,
  createFFMPEGLocalClient,
  createFFMPEGClientFromEnv
} from './FFMPEGClientFactory';

// Other existing clients
export { Chatterbox<PERSON>IClient } from './ChatterboxAPIClient';
export { Whisper<PERSON><PERSON>lient } from './WhisperAPIClient';

// Re-export default factory for convenience
export { default as FFMPEGFactory } from './FFMPEGClientFactory';

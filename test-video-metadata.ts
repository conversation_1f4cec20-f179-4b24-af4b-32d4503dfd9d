/**
 * Test Video Metadata Extraction (No API Calls)
 * 
 * Tests the Video role's ability to automatically extract metadata
 * from video files using ffprobe without making expensive API calls.
 */

import { Video } from './src/media/assets/roles';
import { SmartAssetFactory } from './src/media/assets/SmartAssetFactory';
import * as fs from 'fs';
import * as path from 'path';

async function testVideoMetadataExtraction() {
  console.log('🎬 Testing Video Metadata Extraction (Local Only)\n');

  // Look for existing video files in the project
  const possibleVideos = [
    './test-videos/sample.mp4',
    './example-5-intro-outro-result.mp4',
    './example-6-multi-intro-outro-result.mp4',
    './test-example1-direct.mp4',
    './output.mp4',
    './test.mp4'
  ];

  let testVideoPath: string | null = null;
  
  // Find an existing video file
  for (const videoPath of possibleVideos) {
    if (fs.existsSync(videoPath)) {
      testVideoPath = videoPath;
      console.log(`✅ Found test video: ${videoPath}`);
      break;
    }
  }

  if (!testVideoPath) {
    console.log('❌ No test video files found in project');
    console.log('📁 Looking for video files...');
    
    // Search for any .mp4 files
    const files = fs.readdirSync('.');
    const mp4Files = files.filter(file => file.endsWith('.mp4'));
    
    if (mp4Files.length > 0) {
      testVideoPath = `./${mp4Files[0]}`;
      console.log(`✅ Found video file: ${testVideoPath}`);
    } else {
      console.log('⚠️ No .mp4 files found. Please add a test video file.');
      return;
    }
  }

  try {
    // Test 1: Create Video directly from file path
    console.log('\n1️⃣ Testing Video.fromFile()...');
    const video1 = Video.fromFile(testVideoPath);
    
    console.log('📊 Video created from file path');
    console.log('   📁 Source file:', testVideoPath);
    console.log('   📏 File size:', fs.statSync(testVideoPath).size, 'bytes');
    
    // Test lazy metadata extraction
    console.log('\n2️⃣ Testing lazy metadata extraction...');
    console.log('   ⏱️  Getting duration...');
    const duration = video1.getDuration();
    console.log(`   ⏱️  Duration: ${duration} seconds`);
    
    console.log('   📐 Getting dimensions...');
    const dimensions = video1.getDimensions();
    console.log(`   📐 Dimensions: ${dimensions.width}x${dimensions.height}`);
      console.log('   🎭 Getting format...');
    const format = video1.format;
    console.log(`   🎭 Format: ${format}`);
    
    // Test 3: Create Video via SmartAssetFactory
    console.log('\n3️⃣ Testing SmartAssetFactory.load()...');
    const smartAsset = SmartAssetFactory.load(testVideoPath);
    console.log('   🏭 Asset created:', smartAsset.constructor.name);
    
    if (smartAsset.hasVideoRole && smartAsset.hasVideoRole()) {
      console.log('   ✅ Asset has video role');
      const video2 = await smartAsset.asVideo();
      
      console.log('   ⏱️  Duration (via SmartAsset):', video2.getDuration());
      console.log('   📐 Dimensions (via SmartAsset):', video2.getDimensions());
    } else {
      console.log('   ❌ Asset does not have video role');
    }
    
    // Test 4: Create Video from Buffer
    console.log('\n4️⃣ Testing Video from Buffer...');
    const videoBuffer = fs.readFileSync(testVideoPath);
    const video3 = new Video(videoBuffer, 'mp4', {}, { sourceFile: testVideoPath });
    
    console.log('   📊 Video created from buffer');
    console.log('   💾 Buffer size:', videoBuffer.length, 'bytes');
    console.log('   ⏱️  Duration (from buffer):', video3.getDuration());
    console.log('   📐 Dimensions (from buffer):', video3.getDimensions());
    
    // Test 5: Compare metadata extraction methods
    console.log('\n5️⃣ Comparing metadata extraction methods...');
    console.table({
      'Video.fromFile': {
        duration: video1.getDuration(),
        width: video1.getDimensions().width,
        height: video1.getDimensions().height,
        format: video1.getFormat()
      },
      'SmartAssetFactory': smartAsset.hasVideoRole && smartAsset.hasVideoRole() ? {
        duration: (await smartAsset.asVideo()).getDuration(),
        width: (await smartAsset.asVideo()).getDimensions().width,
        height: (await smartAsset.asVideo()).getDimensions().height,
        format: (await smartAsset.asVideo()).getFormat()
      } : 'N/A',
      'Buffer Constructor': {
        duration: video3.getDuration(),
        width: video3.getDimensions().width,
        height: video3.getDimensions().height,
        format: video3.getFormat()
      }
    });
    
    // Test 6: Check if metadata is cached (should be faster on second call)
    console.log('\n6️⃣ Testing metadata caching...');
    console.time('First getDuration call');
    video1.getDuration();
    console.timeEnd('First getDuration call');
    
    console.time('Second getDuration call (should be cached)');
    video1.getDuration();
    console.timeEnd('Second getDuration call (should be cached)');
    
    console.log('\n✅ Video metadata extraction tests complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Test the createVideoFromUrl method without actually downloading
async function testVideoCreationMethods() {
  console.log('\n🔧 Testing Video Creation Methods (Mock)\n');
  
  try {
    // Test creating video with minimal metadata
    const mockVideoBuffer = Buffer.alloc(1024); // 1KB mock buffer
    const mockVideo = new Video(
      mockVideoBuffer,
      'mp4',
      {
        format: 'mp4',
        url: 'https://example.com/mock-video.mp4',
        fileSize: mockVideoBuffer.length
      }
    );
    
    console.log('📊 Mock video created');
    console.log('   💾 Buffer size:', mockVideoBuffer.length, 'bytes');
    console.log('   🎭 Format:', mockVideo.getFormat());
    console.log('   📄 Metadata:', Object.keys(mockVideo.metadata || {}));
    
    // This will likely fail gracefully since it's not a real video
    console.log('   ⏱️  Duration (mock):', mockVideo.getDuration());
    console.log('   📐 Dimensions (mock):', mockVideo.getDimensions());
    
  } catch (error) {
    console.log('⚠️ Expected error with mock video:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  (async () => {
    try {
      await testVideoMetadataExtraction();
      await testVideoCreationMethods();
    } catch (error) {
      console.error('Test suite failed:', error);
    }
  })();
}

export { testVideoMetadataExtraction, testVideoCreationMethods };

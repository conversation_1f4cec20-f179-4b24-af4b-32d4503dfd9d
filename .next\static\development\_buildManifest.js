self.__BUILD_MANIFEST = (function(a,b){return {__rewrites:{afterFiles:[{has:a,source:"\u002Ffeed.xml",destination:b},{has:a,source:"\u002Frss.xml",destination:b},{has:a,source:"\u002Fblog\u002Ffeed.xml",destination:b},{has:a,source:"\u002Fblog\u002Frss.xml",destination:b}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,sortedPages:["\u002F_app"]}}(void 0,"\u002Fapi\u002Fblog\u002Ffeed"));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()
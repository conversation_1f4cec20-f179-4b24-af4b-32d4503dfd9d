/**
 * Provider Role Type Definitions
 * 
 * Core interfaces that define what capabilities a provider can offer.
 * Each role represents a specific AI/ML capability.
 */

import { SpeechToTextModel } from '../../models/SpeechToTextModel';
import { TextToSpeechModel } from '../../models/TextToSpeechModel';
import { VideoToAudioModel } from '../../models/VideoToAudioModel';
import { TextToVideoModel } from '../../models/TextToVideoModel';
import { VideoToVideoModel } from '../../models/VideoToVideoModel';

/**
 * Base service management interface for providers that manage services (like Docker providers)
 */
export interface ServiceManagement {
  startService(): Promise<boolean>;
  stopService(): Promise<boolean>;
  getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }>;
}

/**
 * Speech-to-Text Provider Role
 */
export interface SpeechToTextProvider extends ServiceManagement {
  createSpeechToTextModel(modelId: string): Promise<SpeechToTextModel>;
  getSupportedSpeechToTextModels(): string[];
  supportsSpeechToTextModel(modelId: string): boolean;
}

/**
 * Text-to-Speech Provider Role
 */
export interface TextToSpeechProvider extends ServiceManagement {
  createTextToSpeechModel(modelId: string): Promise<TextToSpeechModel>;
  getSupportedTextToSpeechModels(): string[];
  supportsTextToSpeechModel(modelId: string): boolean;
}

/**
 * Video-to-Audio Provider Role
 */
export interface VideoToAudioProvider extends ServiceManagement {
  createVideoToAudioModel(modelId: string): Promise<VideoToAudioModel>;
  getSupportedVideoToAudioModels(): string[];
  supportsVideoToAudioModel(modelId: string): boolean;
}

/**
 * Text-to-Video Provider Role
 */
export interface TextToVideoProvider {
  createTextToVideoModel(modelId: string): Promise<TextToVideoModel>;
  getSupportedTextToVideoModels(): string[];
  supportsTextToVideoModel(modelId: string): boolean;
}

/**
 * Video-to-Video Provider Role (composition, filters, etc.)
 */
export interface VideoToVideoProvider extends ServiceManagement {
  createVideoToVideoModel(modelId: string): Promise<VideoToVideoModel>;
  getSupportedVideoToVideoModels(): string[];
  supportsVideoToVideoModel(modelId: string): boolean;
}

/**
 * Text-to-Image Provider Role
 */
export interface TextToImageProvider {
  createTextToImageModel(modelId: string): Promise<any>; // TODO: Define TextToImageModel
  getSupportedTextToImageModels(): string[];
  supportsTextToImageModel(modelId: string): boolean;
}

/**
 * Text Generation Provider Role
 */
export interface TextGenerationProvider {
  createTextGenerationModel(modelId: string): Promise<any>; // TODO: Define TextGenerationModel
  getSupportedTextGenerationModels(): string[];
  supportsTextGenerationModel(modelId: string): boolean;
}

/**
 * Constructor type for mixin functions
 */
export type Constructor<T = {}> = new (...args: any[]) => T;

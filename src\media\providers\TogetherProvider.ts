/**
 * Together AI Provider with TextToText Support
 * 
 * Provider that integrates with Together.ai's unified LLM API.
 * Provides access to open-source models through Together's platform.
 */

import {
  MediaProvider,
  ProviderType,
  MediaCapability,
  ProviderModel,
  ProviderConfig,
  GenerationRequest,
  GenerationResult
} from '../types/provider';
import { TogetherAPIClient, TogetherConfig } from '../clients/TogetherAPIClient';
import { TextToTextProvider } from './roles';
import { TextToTextModel } from '../models/TextToTextModel';
import { TogetherTextToTextModel } from '../models/TogetherTextToTextModel';

export class TogetherProvider implements MediaProvider, TextToTextProvider {
  readonly id = 'together';
  readonly name = 'Together AI';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [
    MediaCapability.TEXT_GENERATION,
    MediaCapability.TEXT_TO_TEXT
  ];

  private config?: ProviderConfig;
  private apiClient?: TogetherAPIClient;
  private discoveredModels = new Map<string, ProviderModel>();

  // Pre-configured popular models (can be extended with dynamic discovery)
  private popularModels = [
    'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo',
    'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo',
    'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo',
    'meta-llama/Llama-3.2-3B-Instruct-Turbo',
    'meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo',
    'mistralai/Mixtral-8x7B-Instruct-v0.1',
    'mistralai/Mistral-7B-Instruct-v0.3',
    'Qwen/Qwen2.5-7B-Instruct-Turbo',
    'Qwen/Qwen2.5-72B-Instruct-Turbo',
    'microsoft/DialoGPT-medium',
    'togethercomputer/RedPajama-INCITE-7B-Chat',
    'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO',
    'garage-bAInd/Platypus2-70B-instruct',
    'teknium/OpenHermes-2.5-Mistral-7B',
    'zero-one-ai/Yi-34B-Chat'
  ];

  get models(): ProviderModel[] {
    // Return discovered models if available, otherwise return popular models
    if (this.discoveredModels.size > 0) {
      return Array.from(this.discoveredModels.values());
    }

    return this.popularModels.map(modelId => ({
      id: modelId,
      name: this.getModelDisplayName(modelId),
      description: `Together AI model: ${modelId}`,
      capabilities: [MediaCapability.TEXT_GENERATION, MediaCapability.TEXT_TO_TEXT],
      parameters: {
        temperature: { type: 'number', min: 0, max: 2, default: 0.7 },
        max_tokens: { type: 'number', min: 1, max: 8192, default: 1024 },
        top_p: { type: 'number', min: 0, max: 1, default: 0.9 },
        top_k: { type: 'number', min: 1, max: 100, default: 50 },
        repetition_penalty: { type: 'number', min: 0.1, max: 2, default: 1 }
      },
      pricing: {
        inputCost: 0, // Many Together models are free
        outputCost: 0,
        currency: 'USD'
      }
    }));
  }

  async configure(config: ProviderConfig): Promise<void> {
    this.config = config;
    
    if (!config.apiKey) {
      throw new Error('Together AI API key is required');
    }

    const togetherConfig: TogetherConfig = {
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.together.xyz/v1',
      timeout: config.timeout || 30000
    };

    this.apiClient = new TogetherAPIClient(togetherConfig);

    // Optionally discover available models
    await this.discoverModels();
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiClient) {
      return false;
    }

    try {
      return await this.apiClient.testConnection();
    } catch (error) {
      console.warn('Together AI availability check failed:', error);
      return false;
    }
  }

  getModelsForCapability(capability: MediaCapability): ProviderModel[] {
    if (capability === MediaCapability.TEXT_GENERATION || capability === MediaCapability.TEXT_TO_TEXT) {
      return this.models;
    }
    return [];
  }

  async getHealth() {
    const isAvailable = await this.isAvailable();
    
    return {
      status: isAvailable ? 'healthy' as const : 'unhealthy' as const,
      uptime: process.uptime(),
      activeJobs: 0, // Models handle their own jobs
      queuedJobs: 0,
      lastError: isAvailable ? undefined : 'API connection failed'
    };
  }

  // TextToTextProvider interface implementation
  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    if (!this.apiClient) {
      throw new Error('Provider not configured');
    }

    if (!this.supportsTextToTextModel(modelId)) {
      throw new Error(`Model '${modelId}' is not supported by Together AI provider`);
    }

    return new TogetherTextToTextModel({
      apiClient: this.apiClient,
      modelId
    });
  }

  getSupportedTextToTextModels(): string[] {
    return this.models.map(model => model.id);
  }

  supportsTextToTextModel(modelId: string): boolean {
    return this.getSupportedTextToTextModels().includes(modelId);
  }

  // Service management (no-ops for remote API providers)
  async startService(): Promise<boolean> {
    return true; // Remote APIs are always "started"
  }

  async stopService(): Promise<boolean> {
    return true; // No service to stop for remote APIs
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const isAvailable = await this.isAvailable();
    return {
      running: true, // Remote APIs are always "running"
      healthy: isAvailable,
      error: isAvailable ? undefined : 'API connection failed'
    };
  }

  // MediaProvider interface methods (required but delegated to models)
  async generate(request: GenerationRequest): Promise<GenerationResult> {
    throw new Error('TogetherProvider should use Model instances for generation, not direct generation');
  }

  // Helper methods
  private async discoverModels(): Promise<void> {
    if (!this.apiClient) {
      return;
    }

    try {
      const availableModels = await this.apiClient.getAvailableModels();
      
      for (const model of availableModels) {
        const providerModel: ProviderModel = {
          id: model.id,
          name: model.display_name || model.id,
          description: model.description || `Together AI model: ${model.id}`,
          capabilities: [MediaCapability.TEXT_GENERATION, MediaCapability.TEXT_TO_TEXT],
          parameters: {
            temperature: { type: 'number', min: 0, max: 2, default: 0.7 },
            max_tokens: { type: 'number', min: 1, max: 8192, default: 1024 },
            top_p: { type: 'number', min: 0, max: 1, default: 0.9 },
            top_k: { type: 'number', min: 1, max: 100, default: 50 },
            repetition_penalty: { type: 'number', min: 0.1, max: 2, default: 1 }
          },
          pricing: {
            inputCost: model.pricing?.input || 0,
            outputCost: model.pricing?.output || 0,
            currency: 'USD'
          }
        };

        this.discoveredModels.set(model.id, providerModel);
      }

      console.log(`[TogetherProvider] Discovered ${this.discoveredModels.size} models`);
    } catch (error) {
      console.warn('[TogetherProvider] Model discovery failed, using popular models fallback:', error.message);
    }
  }

  private getModelDisplayName(modelId: string): string {
    const parts = modelId.split('/');
    if (parts.length === 2) {
      const [org, model] = parts;
      return `${org.charAt(0).toUpperCase() + org.slice(1)} ${model.replace(/-/g, ' ')}`;
    }
    return modelId;
  }

  /**
   * Get free models available on Together AI
   */
  getFreeModels(): ProviderModel[] {
    return this.models.filter(model => 
      model.pricing?.inputCost === 0 && model.pricing?.outputCost === 0
    );
  }

  /**
   * Check if a specific model is free
   */
  isModelFree(modelId: string): boolean {
    const model = this.models.find(m => m.id === modelId);
    return model ? (model.pricing?.inputCost === 0 && model.pricing?.outputCost === 0) : false;
  }
}

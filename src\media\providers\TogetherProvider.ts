/**
 * Together AI Provider with TextToText Support
 * 
 * Provider that integrates with Together.ai's unified LLM API.
 * Provides access to open-source models through Together's platform.
 */

import {
  MediaProvider,
  ProviderType,
  MediaCapability,
  ProviderModel,
  ProviderConfig,
  GenerationRequest,
  GenerationResult
} from '../types/provider';
import { TogetherAPIClient, TogetherConfig } from '../clients/TogetherAPIClient';
import { TextToTextProvider, TextToImageProvider } from './roles';
import { TextToTextModel } from '../models/TextToTextModel';
import { TogetherTextToTextModel } from '../models/TogetherTextToTextModel';
import { TextToImageModel } from '../models/TextToImageModel';
import { TogetherTextToImageModel } from '../models/TogetherTextToImageModel';

export class TogetherProvider implements MediaProvider, TextToTextProvider, TextToImageProvider {
  readonly id = 'together';
  readonly name = 'Together AI';
  readonly type = ProviderType.REMOTE;
  readonly capabilities = [
    MediaCapability.TEXT_GENERATION,
    MediaCapability.TEXT_TO_TEXT,
    MediaCapability.IMAGE_GENERATION
  ];

  private config?: ProviderConfig;
  private apiClient?: TogetherAPIClient;
  private discoveredModels = new Map<string, ProviderModel>();

  // Pre-configured popular text models
  private popularTextModels = [
    'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo',
    'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo',
    'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo',
    'meta-llama/Llama-3.2-3B-Instruct-Turbo',
    'meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo',
    'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
    'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free',
    'lgai/exaone-3-5-32b-instruct',
    'mistralai/Mixtral-8x7B-Instruct-v0.1',
    'mistralai/Mistral-7B-Instruct-v0.3',
    'Qwen/Qwen2.5-7B-Instruct-Turbo',
    'Qwen/Qwen2.5-72B-Instruct-Turbo',
    'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO',
    'teknium/OpenHermes-2.5-Mistral-7B'
  ];

  // Pre-configured popular image models
  private popularImageModels = [
    'black-forest-labs/FLUX.1-schnell-Free',
    'black-forest-labs/FLUX.1-schnell',
    'black-forest-labs/FLUX.1-dev',
    'black-forest-labs/FLUX.1-pro',
    'black-forest-labs/FLUX.1.1-pro',
    'black-forest-labs/FLUX.1-redux',
    'black-forest-labs/FLUX.1-canny',
    'black-forest-labs/FLUX.1-depth',
    'black-forest-labs/FLUX.1-kontext-max',
    'black-forest-labs/FLUX.1-kontext-pro',
    'black-forest-labs/FLUX.1-dev-lora'
  ];

  get models(): ProviderModel[] {
    // Return discovered models if available, otherwise return popular models
    if (this.discoveredModels.size > 0) {
      return Array.from(this.discoveredModels.values());
    }

    // Combine text and image models
    const textModels = this.popularTextModels.map(modelId => ({
      id: modelId,
      name: this.getModelDisplayName(modelId),
      description: `Together AI text model: ${modelId}`,
      capabilities: [MediaCapability.TEXT_GENERATION, MediaCapability.TEXT_TO_TEXT],
      parameters: {
        temperature: { type: 'number', min: 0, max: 2, default: 0.7 },
        max_tokens: { type: 'number', min: 1, max: 8192, default: 1024 },
        top_p: { type: 'number', min: 0, max: 1, default: 0.9 },
        top_k: { type: 'number', min: 1, max: 100, default: 50 },
        repetition_penalty: { type: 'number', min: 0.1, max: 2, default: 1 }
      },
      pricing: {
        inputCost: 0, // Many Together models are free
        outputCost: 0,
        currency: 'USD'
      }
    }));

    const imageModels = this.popularImageModels.map(modelId => ({
      id: modelId,
      name: this.getModelDisplayName(modelId),
      description: `Together AI image model: ${modelId}`,
      capabilities: [MediaCapability.IMAGE_GENERATION],
      parameters: this.getImageParametersForModel(modelId),
      pricing: {
        inputCost: 0, // Many FLUX models are free
        outputCost: 0,
        currency: 'USD'
      }
    }));

    return [...textModels, ...imageModels];
  }

  async configure(config: ProviderConfig): Promise<void> {
    this.config = config;
    
    if (!config.apiKey) {
      throw new Error('Together AI API key is required');
    }

    const togetherConfig: TogetherConfig = {
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.together.xyz/v1',
      timeout: config.timeout || 30000
    };

    this.apiClient = new TogetherAPIClient(togetherConfig);

    // Optionally discover available models
    await this.discoverModels();
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiClient) {
      return false;
    }

    try {
      return await this.apiClient.testConnection();
    } catch (error) {
      console.warn('Together AI availability check failed:', error);
      return false;
    }
  }

  getModelsForCapability(capability: MediaCapability): ProviderModel[] {
    return this.models.filter(model =>
      model.capabilities.includes(capability)
    );
  }

  async getHealth() {
    const isAvailable = await this.isAvailable();
    
    return {
      status: isAvailable ? 'healthy' as const : 'unhealthy' as const,
      uptime: process.uptime(),
      activeJobs: 0, // Models handle their own jobs
      queuedJobs: 0,
      lastError: isAvailable ? undefined : 'API connection failed'
    };
  }

  // TextToTextProvider interface implementation
  async createTextToTextModel(modelId: string): Promise<TextToTextModel> {
    if (!this.apiClient) {
      throw new Error('Provider not configured');
    }

    if (!this.supportsTextToTextModel(modelId)) {
      throw new Error(`Model '${modelId}' is not supported by Together AI provider`);
    }

    return new TogetherTextToTextModel({
      apiClient: this.apiClient,
      modelId
    });
  }

  getSupportedTextToTextModels(): string[] {
    return this.models.map(model => model.id);
  }

  supportsTextToTextModel(modelId: string): boolean {
    return this.getSupportedTextToTextModels().includes(modelId);
  }

  // TextToImageProvider interface implementation
  async createTextToImageModel(modelId: string): Promise<TextToImageModel> {
    if (!this.apiClient) {
      throw new Error('Provider not configured');
    }

    if (!this.supportsTextToImageModel(modelId)) {
      throw new Error(`Image model '${modelId}' is not supported by Together AI provider`);
    }

    // Get model metadata dynamically
    const modelMetadata = await this.apiClient.getModelInfo(modelId);

    return new TogetherTextToImageModel({
      apiClient: this.apiClient,
      modelId,
      modelMetadata
    });
  }

  getSupportedTextToImageModels(): string[] {
    return this.getModelsForCapability(MediaCapability.IMAGE_GENERATION).map(model => model.id);
  }

  supportsTextToImageModel(modelId: string): boolean {
    return this.getSupportedTextToImageModels().includes(modelId);
  }

  // Service management (no-ops for remote API providers)
  async startService(): Promise<boolean> {
    return true; // Remote APIs are always "started"
  }

  async stopService(): Promise<boolean> {
    return true; // No service to stop for remote APIs
  }

  async getServiceStatus(): Promise<{ running: boolean; healthy: boolean; error?: string }> {
    const isAvailable = await this.isAvailable();
    return {
      running: true, // Remote APIs are always "running"
      healthy: isAvailable,
      error: isAvailable ? undefined : 'API connection failed'
    };
  }

  // MediaProvider interface methods (required but delegated to models)
  async generate(request: GenerationRequest): Promise<GenerationResult> {
    throw new Error('TogetherProvider should use Model instances for generation, not direct generation');
  }

  // Helper methods
  private async discoverModels(): Promise<void> {
    if (!this.apiClient) {
      return;
    }

    try {
      const availableModels = await this.apiClient.getAvailableModels();
      
      for (const model of availableModels) {
        // Determine model capabilities based on type and name
        const capabilities = this.determineModelCapabilities(model);

        // Skip models that don't match our supported capabilities
        if (capabilities.length === 0) {
          continue;
        }

        // Set parameters based on model type
        const parameters = this.getParametersForCapabilities(capabilities, model.id);

        const providerModel: ProviderModel = {
          id: model.id,
          name: model.display_name || model.id,
          description: model.description || `Together AI model: ${model.id}`,
          capabilities,
          parameters,
          pricing: {
            inputCost: model.pricing?.input || 0,
            outputCost: model.pricing?.output || 0,
            currency: 'USD'
          }
        };

        this.discoveredModels.set(model.id, providerModel);
      }

      console.log(`[TogetherProvider] Discovered ${this.discoveredModels.size} models`);
    } catch (error) {
      console.warn('[TogetherProvider] Model discovery failed, using popular models fallback:', error.message);
    }
  }

  private getModelDisplayName(modelId: string): string {
    const parts = modelId.split('/');
    if (parts.length === 2) {
      const [org, model] = parts;
      return `${org.charAt(0).toUpperCase() + org.slice(1)} ${model.replace(/-/g, ' ')}`;
    }
    return modelId;
  }

  /**
   * Get free models available on Together AI
   */
  getFreeModels(): ProviderModel[] {
    return this.models.filter(model => 
      model.pricing?.inputCost === 0 && model.pricing?.outputCost === 0
    );
  }

  /**
   * Check if a specific model is free
   */
  isModelFree(modelId: string): boolean {
    const model = this.models.find(m => m.id === modelId);
    return model ? (model.pricing?.inputCost === 0 && model.pricing?.outputCost === 0) : false;
  }

  /**
   * Determine model capabilities based on model metadata
   */
  private determineModelCapabilities(model: any): MediaCapability[] {
    const capabilities: MediaCapability[] = [];

    // Check for image generation models
    if (model.type === 'image' ||
        model.id.includes('FLUX') ||
        model.id.includes('stable-diffusion') ||
        model.id.includes('dall-e') ||
        model.display_name?.toLowerCase().includes('image')) {
      capabilities.push(MediaCapability.IMAGE_GENERATION);
    }

    // Check for text generation models (default for most models)
    if (model.type === 'chat' ||
        model.type === 'language' ||
        !model.type || // Default to text if no type specified
        model.id.includes('instruct') ||
        model.id.includes('chat') ||
        model.display_name?.toLowerCase().includes('instruct')) {
      capabilities.push(MediaCapability.TEXT_GENERATION, MediaCapability.TEXT_TO_TEXT);
    }

    return capabilities;
  }

  /**
   * Get appropriate parameters based on model capabilities (dynamic)
   */
  private getParametersForCapabilities(capabilities: MediaCapability[], modelId?: string): Record<string, any> {
    if (capabilities.includes(MediaCapability.IMAGE_GENERATION)) {
      // For image models, use dynamic parameters
      return this.getImageParametersForModel(modelId || '');
    } else {
      // For text models, use standard LLM parameters
      return {
        temperature: { type: 'number', min: 0, max: 2, default: 0.7 },
        max_tokens: { type: 'number', min: 1, max: 8192, default: 1024 },
        top_p: { type: 'number', min: 0, max: 1, default: 0.9 },
        top_k: { type: 'number', min: 1, max: 100, default: 50 },
        repetition_penalty: { type: 'number', min: 0.1, max: 2, default: 1 }
      };
    }
  }

  /**
   * Get models by type for easier access
   */
  getTextModels(): ProviderModel[] {
    return this.getModelsForCapability(MediaCapability.TEXT_GENERATION);
  }

  getImageModels(): ProviderModel[] {
    return this.getModelsForCapability(MediaCapability.IMAGE_GENERATION);
  }

  /**
   * Get dynamic image parameters based on model metadata
   */
  private getImageParametersForModel(modelId: string): Record<string, any> {
    // TODO: Parse actual parameter schema from model metadata when available
    // For now, return generic image generation parameters
    return {
      width: { type: 'number', min: 256, max: 2048, default: 1024 },
      height: { type: 'number', min: 256, max: 2048, default: 1024 },
      steps: { type: 'number', min: 1, max: 50, default: 20 },
      seed: { type: 'number', min: 0, max: **********, default: null },
      negative_prompt: { type: 'string', default: '' }
    };
  }
}

/**
 * Replicate Model Implementations
 * 
 * Specific model implementations for different Replicate model types.
 * Each implements the appropriate model interface and uses Replicate API.
 */

import { ModelMetadata } from './Model';
import { TextToSpeechModel, TextToSpeechOptions } from './TextToSpeechModel';
import { ReplicateClient, ReplicateModelMetadata } from '../clients/ReplicateClient';
import { Text, Speech, Audio } from '../assets/roles';
import { TextInput, castToText } from '../assets/casting';
import Replicate from 'replicate';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export interface ReplicateModelConfig {
  client: ReplicateClient;
  modelMetadata: ReplicateModelMetadata;
  replicateClient: Replicate;
}

/**
 * ReplicateTextToSpeechModel - Implements TextToSpeechModel for Replicate TTS models
 * 
 * Takes Replicate TTS model metadata (like chatterbox) and uses Replicate API for TTS
 */
export class ReplicateTextToSpeechModel extends TextToSpeechModel {
  private client: ReplicateClient;
  private modelMetadata: ReplicateModelMetadata;
  private replicateClient: Replicate;

  constructor(config: ReplicateModelConfig) {
    // Create metadata for TextToSpeechModel
    const metadata: ModelMetadata = {
      id: config.modelMetadata.id,
      name: config.modelMetadata.name || config.modelMetadata.id,
      description: config.modelMetadata.description || '',
      version: '1.0.0',
      provider: 'replicate',
      capabilities: ['text-to-speech'],
      inputTypes: ['text'],
      outputTypes: ['audio']
    };

    super(metadata);

    this.client = config.client;
    this.modelMetadata = config.modelMetadata;
    this.replicateClient = config.replicateClient;
  }

  /**
   * Transform text to speech using specific Replicate TTS model
   */
  async transform(input: TextInput, options?: TextToSpeechOptions): Promise<Speech>;
  async transform(text: TextInput, voiceAudio: Speech, options?: TextToSpeechOptions): Promise<Speech>;
  async transform(input: TextInput, voiceAudioOrOptions?: Speech | TextToSpeechOptions, options?: TextToSpeechOptions): Promise<Speech> {
    // Cast input to Text
    const text = await castToText(input);

    if (!text.isValid()) {
      throw new Error('Invalid text data provided');
    }

    // Determine if second parameter is voice audio or options
    let voiceAudio: Speech | undefined;
    let actualOptions: TextToSpeechOptions | undefined;

    if (voiceAudioOrOptions && typeof voiceAudioOrOptions === 'object' && 'data' in voiceAudioOrOptions) {
      // Second parameter is Speech (voice audio)
      voiceAudio = voiceAudioOrOptions as Speech;
      actualOptions = options;
    } else {
      // Second parameter is options
      actualOptions = voiceAudioOrOptions as TextToSpeechOptions;
    }

    try {
      // Prepare input for this specific Replicate TTS model
      const replicateInput = this.prepareReplicateInput(text.content, voiceAudio, actualOptions);

      // Create prediction using Replicate API
      const prediction = await this.replicateClient.predictions.create({
        version: this.modelMetadata.id,
        input: replicateInput
      });

      // Wait for completion (simplified - you'd want to poll properly)
      const finalPrediction = await this.replicateClient.predictions.get(prediction.id);      if (finalPrediction.status === 'succeeded') {
        console.log(`[ReplicateTextToSpeech] Audio generated:`, finalPrediction.output);
        
        // Create Speech from result URL - ACTUALLY DOWNLOAD THE FILE
        const speech = await this.createSpeechFromUrl(
          Array.isArray(finalPrediction.output) ? finalPrediction.output[0] : finalPrediction.output,
          {
            originalText: text.content,
            modelUsed: this.modelMetadata.id,
            options: actualOptions,
            predictionId: prediction.id
          }
        );

        return speech;
      } else if (finalPrediction.status === 'failed') {
        throw new Error(String(finalPrediction.error) || 'TTS prediction failed');
      } else {
        throw new Error(`TTS prediction in unexpected state: ${finalPrediction.status}`);
      }
    } catch (error) {
      throw new Error(`Replicate TTS failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Prepare input for specific Replicate TTS model based on its parameters
   */
  private prepareReplicateInput(text: string, voiceAudio?: Speech, options?: TextToSpeechOptions): any {
    const input: any = {
      text: text
    };

    // Add model-specific parameters based on the model's parameter schema
    const params = this.modelMetadata.parameters || {};

    // Add voice cloning if provided and model supports it
    if (voiceAudio && params.voice_audio) {
      console.log('Voice cloning requested but file upload not yet implemented');
      // TODO: Upload voice audio file to Replicate and get URL
    }

    // Add common TTS options if model supports them
    if (options?.language && params.language) {
      input.language = options.language;
    }
    if (options?.speed && params.speed) {
      input.speed = options.speed;
    }

    // Add any default parameters from model metadata
    Object.keys(params).forEach(paramName => {
      const param = params[paramName];
      if (param.default !== undefined && !(paramName in input)) {
        input[paramName] = param.default;
      }
    });

    return input;
  }

  /**
   * Check if model is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await this.client.testConnection();
    } catch {
      return false;
    }
  }

  /**
   * Create Speech object from URL - ACTUALLY DOWNLOADS THE FILE
   */
  private async createSpeechFromUrl(url: string, metadata: any = {}): Promise<Speech> {
    try {
      console.log(`[ReplicateTextToSpeech] Downloading audio from: ${url}`);
      
      // Download the audio file
      const audioBuffer = await this.downloadFile(url);
      
      // Create a local file path
      const tempDir = path.join(os.tmpdir(), 'replicate-audio');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      // Determine file extension from URL or default to mp3
      const urlExt = path.extname(new URL(url).pathname);
      const extension = urlExt || '.mp3';
      const filename = `speech_${Date.now()}_${Math.random().toString(36).substring(7)}${extension}`;
      const localPath = path.join(tempDir, filename);
      
      // Save the audio to disk
      fs.writeFileSync(localPath, audioBuffer);
      
      console.log(`[ReplicateTextToSpeech] Audio saved to: ${localPath} (${(audioBuffer.length / 1024 / 1024).toFixed(2)} MB)`);
      
      // Create Speech object with REAL audio data
      return new Speech(
        audioBuffer, // ACTUAL audio data, not empty buffer!
        {
          url: url,
          localPath: localPath,
          fileSize: audioBuffer.length,
          format: extension.replace('.', ''),
          ...metadata
        }
      );
      
    } catch (error) {
      console.error(`[ReplicateTextToSpeech] Failed to download audio:`, error);
      
      // Fallback: create Speech with URL only (but log the failure)
      console.warn(`[ReplicateTextToSpeech] Falling back to URL-only Speech object`);
      return new Speech(
        Buffer.alloc(0),
        {
          url: url,
          downloadError: error.message,
          ...metadata
        }
      );
    }
  }

  /**
   * Download file from URL and return Buffer
   */
  private async downloadFile(url: string, timeout: number = 60000): Promise<Buffer> {
    const https = require('https');
    const http = require('http');
    
    return new Promise((resolve, reject) => {
      const client = url.startsWith('https:') ? https : http;
      
      const request = client.get(url, (response: any) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }
        
        const chunks: Buffer[] = [];
        let totalSize = 0;
        const contentLength = parseInt(response.headers['content-length'] || '0');
        
        response.on('data', (chunk: Buffer) => {
          chunks.push(chunk);
          totalSize += chunk.length;
          
          // Show download progress for larger files
          if (contentLength > 0) {
            const progress = ((totalSize / contentLength) * 100).toFixed(1);
            if (totalSize % (512 * 1024) < chunk.length) { // Log every ~512KB for audio
              console.log(`[ReplicateTextToSpeech] Download progress: ${progress}% (${(totalSize / 1024).toFixed(1)}KB)`);
            }
          }
        });
        
        response.on('end', () => {
          const buffer = Buffer.concat(chunks);
          console.log(`[ReplicateTextToSpeech] Download complete: ${(buffer.length / 1024).toFixed(2)} KB`);
          resolve(buffer);
        });
        
        response.on('error', reject);
      });
      
      request.on('error', reject);
      request.setTimeout(timeout, () => {
        request.destroy();
        reject(new Error(`Download timeout after ${timeout}ms`));
      });
    });
  }
}

// TODO: Create similar classes for other model types:

/**
 * ReplicateTextToImageModel - Implements TextToImageModel for Replicate image models
 */
// export class ReplicateTextToImageModel extends TextToImageModel { ... }

/**
 * ReplicateTextToVideoModel - Implements TextToVideoModel for Replicate video models  
 */
// export class ReplicateTextToVideoModel extends TextToVideoModel { ... }

/**
 * ReplicateVideoToVideoModel - Implements VideoToVideoModel for Replicate video processing
 */
// export class ReplicateVideoToVideoModel extends VideoToVideoModel { ... }

export default ReplicateTextToSpeechModel;

/**
 * ReplicateProvider with File Saving
 * 
 * Enhanced example that downloads and saves generated content to disk
 */

import { ReplicateProvider } from './packages/providers/src/remote/ReplicateProvider';
import { Text } from './src/media/assets/roles';
import * as fs from 'fs';
import * as path from 'path';
import fetch from 'node-fetch';

// Helper function to download and save files
async function downloadAndSave(url: string, outputPath: string): Promise<string> {
  console.log(`📥 Downloading: ${url}`);
  
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download: ${response.statusText}`);
  }

  // Ensure output directory exists
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Download and save file
  const buffer = await response.buffer();
  fs.writeFileSync(outputPath, buffer);
  
  console.log(`💾 Saved to: ${outputPath} (${(buffer.length / 1024 / 1024).toFixed(2)} MB)`);
  return outputPath;
}

// Helper to get file extension from URL or content type
function getFileExtension(url: string, contentType?: string): string {
  // Try to get extension from URL
  const urlExt = path.extname(new URL(url).pathname);
  if (urlExt) return urlExt;
  
  // Fallback to content type
  if (contentType?.includes('mp4')) return '.mp4';
  if (contentType?.includes('webm')) return '.webm';
  if (contentType?.includes('mp3')) return '.mp3';
  if (contentType?.includes('wav')) return '.wav';
  if (contentType?.includes('png')) return '.png';
  if (contentType?.includes('jpg') || contentType?.includes('jpeg')) return '.jpg';
  
  return '.bin'; // Unknown format
}

async function generateAndSaveContent() {
  console.log('🎬 ReplicateProvider - Generate and Save Content\n');

  // Setup output directory
  const outputDir = './generated-content';
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Configure provider
  const provider = new ReplicateProvider();
  await provider.configure({
    apiKey: process.env.REPLICATE_API_KEY || 'r8_your_api_key_here'
  });

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  // 1. Generate and save video
  console.log('🎥 Generating Video...');
  try {
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
    
    const video = await videoModel.transform(Text.fromString("A majestic eagle soaring over snow-capped mountains"), {
      duration: 5,
      aspectRatio: '16:9',
      fps: 24
    });

    // Get the video URL from metadata
    const videoUrl = (video as any).metadata?.url;
    if (videoUrl) {
      const videoPath = path.join(outputDir, `video_${timestamp}.mp4`);
      await downloadAndSave(videoUrl, videoPath);
      
      console.log('✅ Video saved successfully!');
      console.log(`   📁 Location: ${path.resolve(videoPath)}`);
      console.log(`   🎬 Duration: ${video.getDuration()} seconds`);
      console.log(`   📐 Dimensions: ${JSON.stringify(video.getDimensions())}\n`);
    } else {
      console.log('⚠️ No video URL found in result\n');
    }
  } catch (error) {
    console.log('❌ Video generation failed:', error.message, '\n');
  }

  // 2. Generate and save audio (when TTS models are available)
  console.log('🎤 Generating Audio...');
  try {
    // This would work with an actual TTS model
    const ttsModel = await provider.createTextToAudioModel('some-tts-model');

    const speech = await ttsModel.transform(Text.fromString("Welcome to the future of AI-generated content!"), {
      language: 'en',
      speed: 1.0
    });

    // Get the audio URL from metadata
    const audioUrl = (speech as any).metadata?.resultUrl;
    if (audioUrl) {
      const audioPath = path.join(outputDir, `speech_${timestamp}.mp3`);
      await downloadAndSave(audioUrl, audioPath);
      
      console.log('✅ Speech saved successfully!');
      console.log(`   📁 Location: ${path.resolve(audioPath)}\n`);
    } else {
      console.log('⚠️ No audio URL found in result\n');
    }
  } catch (error) {
    console.log('❌ Speech generation failed:', error.message, '\n');
  }

  // 3. Generate and save image (when implemented)
  console.log('🎨 Generating Image...');
  try {
    // This will work once TextToImageModel is implemented
    const imageModel = await provider.createTextToImageModel('black-forest-labs/flux-1.1-pro');
    
    const image = await imageModel.transform("A cyberpunk cityscape at sunset with neon lights", {
      width: 1024,
      height: 1024,
      steps: 20,
      seed: 42
    });

    const imageUrl = (image as any).metadata?.url;
    if (imageUrl) {
      const imagePath = path.join(outputDir, `image_${timestamp}.png`);
      await downloadAndSave(imageUrl, imagePath);
      
      console.log('✅ Image saved successfully!');
      console.log(`   📁 Location: ${path.resolve(imagePath)}\n`);
    }
  } catch (error) {
    console.log('❌ Image generation failed (not implemented yet):', error.message, '\n');
  }

  // 4. Batch generation and saving
  console.log('🔄 Batch Generation...');
  const prompts = [
    "A serene lake at dawn",
    "A bustling marketplace in Morocco", 
    "Northern lights dancing over a forest"
  ];

  try {
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
    
    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      console.log(`   🎬 Generating video ${i + 1}/3: "${prompt}"`);
      
      try {
        const video = await videoModel.transform(Text.fromString(prompt), {
          duration: 3,
          aspectRatio: '1:1', // Square format
          seed: i * 42 // Different seed for variety
        });

        const videoUrl = (video as any).metadata?.url;
        if (videoUrl) {
          const videoPath = path.join(outputDir, `batch_video_${i + 1}_${timestamp}.mp4`);
          await downloadAndSave(videoUrl, videoPath);
          console.log(`   ✅ Batch video ${i + 1} saved!`);
        }
      } catch (error) {
        console.log(`   ❌ Batch video ${i + 1} failed:`, error.message);
      }
    }
  } catch (error) {
    console.log('❌ Batch generation failed:', error.message);
  }

  console.log('\n📁 All generated content saved to:', path.resolve(outputDir));
  console.log('🎉 Generation complete!');
}

// Enhanced function with metadata saving
async function generateWithMetadata() {
  console.log('\n📊 Generating with Metadata Tracking\n');

  const provider = new ReplicateProvider();
  await provider.configure({
    apiKey: process.env.REPLICATE_API_KEY || 'r8_your_api_key_here'
  });

  const outputDir = './generated-content';
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  try {
    const videoModel = await provider.createTextToVideoModel('luma/dream-machine');
    
    const prompt = "A robot playing chess in a futuristic library";
    const options = {
      duration: 8,
      aspectRatio: '16:9',
      fps: 30,
      seed: 123
    };

    console.log('🎬 Generating video with metadata tracking...');
    const startTime = Date.now();
    
    const video = await videoModel.transform(Text.fromString(prompt), options);
    
    const generationTime = Date.now() - startTime;
    const videoUrl = (video as any).metadata?.url;

    if (videoUrl) {
      // Save video file
      const videoPath = path.join(outputDir, `video_with_metadata_${timestamp}.mp4`);
      await downloadAndSave(videoUrl, videoPath);

      // Save metadata file
      const metadata = {
        prompt,
        options,
        generationTime: `${generationTime}ms`,
        timestamp: new Date().toISOString(),
        model: 'luma/dream-machine',
        provider: 'replicate',
        videoUrl,
        localPath: videoPath,
        fileSize: fs.statSync(videoPath).size,
        duration: video.getDuration(),
        dimensions: video.getDimensions(),
        metadata: (video as any).metadata
      };

      const metadataPath = path.join(outputDir, `video_metadata_${timestamp}.json`);
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      console.log('✅ Video and metadata saved!');
      console.log(`   🎬 Video: ${path.resolve(videoPath)}`);
      console.log(`   📋 Metadata: ${path.resolve(metadataPath)}`);
      console.log(`   ⏱️  Generation time: ${generationTime}ms`);
      console.log(`   📏 File size: ${(metadata.fileSize / 1024 / 1024).toFixed(2)} MB`);
    }
  } catch (error) {
    console.log('❌ Generation with metadata failed:', error.message);
  }
}

// Run the examples
if (require.main === module) {
  (async () => {
    try {
      await generateAndSaveContent();
      await generateWithMetadata();
    } catch (error) {
      console.error('Demo failed:', error);
    }
  })();
}

export { generateAndSaveContent, generateWithMetadata, downloadAndSave };
